// Langflow API integration service

export interface LangflowConfig {
  baseUrl: string;
  flowId: string;
  apiKey?: string;
}

export interface LangflowMessage {
  input_value: string;
  output_type: "chat" | "text" | "any" | "debug";
  input_type: "chat" | "text";
  session_id?: string;
  tweaks?: Record<string, unknown>;
}

export interface LangflowResponse {
  session_id: string;
  outputs: Array<{
    inputs: Record<string, unknown>;
    outputs: Array<{
      results: {
        message: {
          text: string;
          sender: string;
          sender_name: string;
          session_id: string;
          timestamp: string;
          flow_id: string;
          properties?: Record<string, unknown>;
        };
      };
    }>;
  }>;
}

export interface LangflowStreamEvent {
  event: "add_message" | "token" | "end";
  data: {
    chunk?: string;
    result?: LangflowResponse;
    [key: string]: unknown;
  };
}

export class LangflowService {
  private config: LangflowConfig;

  constructor(config: LangflowConfig) {
    this.config = config;
  }

  /**
   * Send a message to Lang<PERSON> and get a response
   */
  async sendMessage(
    message: string,
    sessionId?: string,
    options?: Partial<LangflowMessage>
  ): Promise<LangflowResponse> {
    if (!message?.trim()) {
      throw new Error("Message cannot be empty");
    }

    if (!this.config.baseUrl || !this.config.flowId) {
      throw new Error("Langflow configuration is incomplete. Please check baseUrl and flowId.");
    }

    const payload: LangflowMessage = {
      input_value: message.trim(),
      output_type: "chat",
      input_type: "chat",
      session_id: sessionId || this.generateSessionId(),
      ...options,
    };

    const requestOptions: RequestInit = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(this.config.apiKey && { "x-api-key": this.config.apiKey }),
      },
      body: JSON.stringify(payload),
      // Add timeout for production
      signal: AbortSignal.timeout(30000), // 30 second timeout
    };

    try {
      const response = await fetch(
        `${this.config.baseUrl}/api/v1/run/${this.config.flowId}`,
        requestOptions
      );

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      // Validate response structure
      if (!data || typeof data !== 'object') {
        throw new Error("Invalid response format from Langflow");
      }

      return data;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error("Request timeout - please try again");
        }
        if (error.message.includes('fetch')) {
          throw new Error("Network error - please check your connection and Langflow server");
        }
      }
      console.error("Error sending message to Langflow:", error);
      throw error;
    }
  }

  /**
   * Send a message to Langflow with streaming response
   */
  async sendMessageStream(
    message: string,
    sessionId?: string,
    onToken?: (token: string) => void,
    onComplete?: (response: LangflowResponse) => void,
    options?: Partial<LangflowMessage>
  ): Promise<void> {
    if (!message?.trim()) {
      throw new Error("Message cannot be empty");
    }

    if (!this.config.baseUrl || !this.config.flowId) {
      throw new Error("Langflow configuration is incomplete. Please check baseUrl and flowId.");
    }

    const payload: LangflowMessage = {
      input_value: message.trim(),
      output_type: "chat",
      input_type: "chat",
      session_id: sessionId || this.generateSessionId(),
      ...options,
    };

    const abortController = new AbortController();
    const timeoutId = setTimeout(() => abortController.abort(), 60000); // 60 second timeout for streaming

    const requestOptions: RequestInit = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        accept: "application/json",
        ...(this.config.apiKey && { "x-api-key": this.config.apiKey }),
      },
      body: JSON.stringify(payload),
      signal: abortController.signal,
    };

    try {
      const response = await fetch(
        `${this.config.baseUrl}/api/v1/run/${this.config.flowId}?stream=true`,
        requestOptions
      );

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Streaming not supported - no response body reader available");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.trim()) {
              try {
                const event: LangflowStreamEvent = JSON.parse(line);

                if (event.event === "token" && onToken && event.data?.chunk) {
                  onToken(event.data.chunk);
                } else if (event.event === "end" && onComplete && event.data?.result) {
                  onComplete(event.data.result);
                }
              } catch (parseError) {
                console.warn("Failed to parse streaming event:", line, parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
        clearTimeout(timeoutId);
      }
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error("Streaming request timeout - please try again");
        }
        if (error.message.includes('fetch')) {
          throw new Error("Network error during streaming - please check your connection");
        }
      }
      console.error("Error streaming message from Langflow:", error);
      throw error;
    }
  }

  /**
   * Extract the text content from a Langflow response
   */
  extractMessageText(response: LangflowResponse): string {
    try {
      if (!response || typeof response !== 'object') {
        console.warn("Invalid response object provided to extractMessageText");
        return "";
      }

      const text = response.outputs?.[0]?.outputs?.[0]?.results?.message?.text;

      if (typeof text === 'string') {
        return text.trim();
      }

      console.warn("No valid text found in Langflow response");
      return "";
    } catch (error) {
      console.error("Error extracting message text:", error);
      return "";
    }
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<LangflowConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Default configuration - update these values for your setup
export const defaultLangflowConfig: LangflowConfig = {
  baseUrl: "http://127.0.0.1:7860",
  flowId: "8b59d93f-02bc-40a0-9adc-a5c7ea18b860", // Replace with your actual flow ID
  // apiKey: "your-api-key-here", // Optional: only needed if authentication is enabled
};

// Create a default instance
export const langflowService = new LangflowService(defaultLangflowConfig);
