var ng=Object.defineProperty;var ic=e=>{throw TypeError(e)};var rg=(e,t,n)=>t in e?ng(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Zs=(e,t,n)=>rg(e,typeof t!="symbol"?t+"":t,n),Js=(e,t,n)=>t.has(e)||ic("Cannot "+n);var N=(e,t,n)=>(Js(e,t,"read from private field"),n?n.call(e):t.get(e)),q=(e,t,n)=>t.has(e)?ic("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),W=(e,t,n,r)=>(Js(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),ke=(e,t,n)=>(Js(e,t,"access private method"),n);var ri=(e,t,n,r)=>({set _(o){W(e,t,o,n)},get _(){return N(e,t,r)}});function og(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function rf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var of={exports:{}},ws={},sf={exports:{}},Y={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qo=Symbol.for("react.element"),ig=Symbol.for("react.portal"),sg=Symbol.for("react.fragment"),lg=Symbol.for("react.strict_mode"),ag=Symbol.for("react.profiler"),ug=Symbol.for("react.provider"),cg=Symbol.for("react.context"),dg=Symbol.for("react.forward_ref"),fg=Symbol.for("react.suspense"),pg=Symbol.for("react.memo"),hg=Symbol.for("react.lazy"),sc=Symbol.iterator;function mg(e){return e===null||typeof e!="object"?null:(e=sc&&e[sc]||e["@@iterator"],typeof e=="function"?e:null)}var lf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},af=Object.assign,uf={};function Wr(e,t,n){this.props=e,this.context=t,this.refs=uf,this.updater=n||lf}Wr.prototype.isReactComponent={};Wr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Wr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function cf(){}cf.prototype=Wr.prototype;function Va(e,t,n){this.props=e,this.context=t,this.refs=uf,this.updater=n||lf}var Ha=Va.prototype=new cf;Ha.constructor=Va;af(Ha,Wr.prototype);Ha.isPureReactComponent=!0;var lc=Array.isArray,df=Object.prototype.hasOwnProperty,Wa={current:null},ff={key:!0,ref:!0,__self:!0,__source:!0};function pf(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)df.call(t,r)&&!ff.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:Qo,type:e,key:i,ref:s,props:o,_owner:Wa.current}}function gg(e,t){return{$$typeof:Qo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Qa(e){return typeof e=="object"&&e!==null&&e.$$typeof===Qo}function vg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ac=/\/+/g;function el(e,t){return typeof e=="object"&&e!==null&&e.key!=null?vg(""+e.key):t.toString(36)}function bi(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Qo:case ig:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+el(s,0):r,lc(o)?(n="",e!=null&&(n=e.replace(ac,"$&/")+"/"),bi(o,t,n,"",function(u){return u})):o!=null&&(Qa(o)&&(o=gg(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(ac,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",lc(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+el(i,l);s+=bi(i,t,n,a,o)}else if(a=mg(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+el(i,l++),s+=bi(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function oi(e,t,n){if(e==null)return e;var r=[],o=0;return bi(e,r,"","",function(i){return t.call(n,i,o++)}),r}function yg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ae={current:null},Ti={transition:null},wg={ReactCurrentDispatcher:Ae,ReactCurrentBatchConfig:Ti,ReactCurrentOwner:Wa};function hf(){throw Error("act(...) is not supported in production builds of React.")}Y.Children={map:oi,forEach:function(e,t,n){oi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return oi(e,function(){t++}),t},toArray:function(e){return oi(e,function(t){return t})||[]},only:function(e){if(!Qa(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Y.Component=Wr;Y.Fragment=sg;Y.Profiler=ag;Y.PureComponent=Va;Y.StrictMode=lg;Y.Suspense=fg;Y.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=wg;Y.act=hf;Y.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=af({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Wa.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)df.call(t,a)&&!ff.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Qo,type:e.type,key:o,ref:i,props:r,_owner:s}};Y.createContext=function(e){return e={$$typeof:cg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:ug,_context:e},e.Consumer=e};Y.createElement=pf;Y.createFactory=function(e){var t=pf.bind(null,e);return t.type=e,t};Y.createRef=function(){return{current:null}};Y.forwardRef=function(e){return{$$typeof:dg,render:e}};Y.isValidElement=Qa;Y.lazy=function(e){return{$$typeof:hg,_payload:{_status:-1,_result:e},_init:yg}};Y.memo=function(e,t){return{$$typeof:pg,type:e,compare:t===void 0?null:t}};Y.startTransition=function(e){var t=Ti.transition;Ti.transition={};try{e()}finally{Ti.transition=t}};Y.unstable_act=hf;Y.useCallback=function(e,t){return Ae.current.useCallback(e,t)};Y.useContext=function(e){return Ae.current.useContext(e)};Y.useDebugValue=function(){};Y.useDeferredValue=function(e){return Ae.current.useDeferredValue(e)};Y.useEffect=function(e,t){return Ae.current.useEffect(e,t)};Y.useId=function(){return Ae.current.useId()};Y.useImperativeHandle=function(e,t,n){return Ae.current.useImperativeHandle(e,t,n)};Y.useInsertionEffect=function(e,t){return Ae.current.useInsertionEffect(e,t)};Y.useLayoutEffect=function(e,t){return Ae.current.useLayoutEffect(e,t)};Y.useMemo=function(e,t){return Ae.current.useMemo(e,t)};Y.useReducer=function(e,t,n){return Ae.current.useReducer(e,t,n)};Y.useRef=function(e){return Ae.current.useRef(e)};Y.useState=function(e){return Ae.current.useState(e)};Y.useSyncExternalStore=function(e,t,n){return Ae.current.useSyncExternalStore(e,t,n)};Y.useTransition=function(){return Ae.current.useTransition()};Y.version="18.3.1";sf.exports=Y;var w=sf.exports;const O=rf(w),xg=og({__proto__:null,default:O},[w]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sg=w,Eg=Symbol.for("react.element"),Cg=Symbol.for("react.fragment"),kg=Object.prototype.hasOwnProperty,Pg=Sg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,bg={key:!0,ref:!0,__self:!0,__source:!0};function mf(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)kg.call(t,r)&&!bg.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Eg,type:e,key:i,ref:s,props:o,_owner:Pg.current}}ws.Fragment=Cg;ws.jsx=mf;ws.jsxs=mf;of.exports=ws;var S=of.exports,gf={exports:{}},Ze={},vf={exports:{}},yf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(k,_){var z=k.length;k.push(_);e:for(;0<z;){var D=z-1>>>1,U=k[D];if(0<o(U,_))k[D]=_,k[z]=U,z=D;else break e}}function n(k){return k.length===0?null:k[0]}function r(k){if(k.length===0)return null;var _=k[0],z=k.pop();if(z!==_){k[0]=z;e:for(var D=0,U=k.length,G=U>>>1;D<G;){var le=2*(D+1)-1,Ve=k[le],Z=le+1,at=k[Z];if(0>o(Ve,z))Z<U&&0>o(at,Ve)?(k[D]=at,k[Z]=z,D=Z):(k[D]=Ve,k[le]=z,D=le);else if(Z<U&&0>o(at,z))k[D]=at,k[Z]=z,D=Z;else break e}}return _}function o(k,_){var z=k.sortIndex-_.sortIndex;return z!==0?z:k.id-_.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],f=1,d=null,c=3,m=!1,y=!1,v=!1,x=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(k){for(var _=n(u);_!==null;){if(_.callback===null)r(u);else if(_.startTime<=k)r(u),_.sortIndex=_.expirationTime,t(a,_);else break;_=n(u)}}function E(k){if(v=!1,g(k),!y)if(n(a)!==null)y=!0,B(C);else{var _=n(u);_!==null&&K(E,_.startTime-k)}}function C(k,_){y=!1,v&&(v=!1,h(P),P=-1),m=!0;var z=c;try{for(g(_),d=n(a);d!==null&&(!(d.expirationTime>_)||k&&!F());){var D=d.callback;if(typeof D=="function"){d.callback=null,c=d.priorityLevel;var U=D(d.expirationTime<=_);_=e.unstable_now(),typeof U=="function"?d.callback=U:d===n(a)&&r(a),g(_)}else r(a);d=n(a)}if(d!==null)var G=!0;else{var le=n(u);le!==null&&K(E,le.startTime-_),G=!1}return G}finally{d=null,c=z,m=!1}}var b=!1,T=null,P=-1,j=5,M=-1;function F(){return!(e.unstable_now()-M<j)}function L(){if(T!==null){var k=e.unstable_now();M=k;var _=!0;try{_=T(!0,k)}finally{_?$():(b=!1,T=null)}}else b=!1}var $;if(typeof p=="function")$=function(){p(L)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,Q=A.port2;A.port1.onmessage=L,$=function(){Q.postMessage(null)}}else $=function(){x(L,0)};function B(k){T=k,b||(b=!0,$())}function K(k,_){P=x(function(){k(e.unstable_now())},_)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(k){k.callback=null},e.unstable_continueExecution=function(){y||m||(y=!0,B(C))},e.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<k?Math.floor(1e3/k):5},e.unstable_getCurrentPriorityLevel=function(){return c},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(k){switch(c){case 1:case 2:case 3:var _=3;break;default:_=c}var z=c;c=_;try{return k()}finally{c=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(k,_){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var z=c;c=k;try{return _()}finally{c=z}},e.unstable_scheduleCallback=function(k,_,z){var D=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?D+z:D):z=D,k){case 1:var U=-1;break;case 2:U=250;break;case 5:U=**********;break;case 4:U=1e4;break;default:U=5e3}return U=z+U,k={id:f++,callback:_,priorityLevel:k,startTime:z,expirationTime:U,sortIndex:-1},z>D?(k.sortIndex=z,t(u,k),n(a)===null&&k===n(u)&&(v?(h(P),P=-1):v=!0,K(E,z-D))):(k.sortIndex=U,t(a,k),y||m||(y=!0,B(C))),k},e.unstable_shouldYield=F,e.unstable_wrapCallback=function(k){var _=c;return function(){var z=c;c=_;try{return k.apply(this,arguments)}finally{c=z}}}})(yf);vf.exports=yf;var Tg=vf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ng=w,qe=Tg;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var wf=new Set,Co={};function Zn(e,t){Ir(e,t),Ir(e+"Capture",t)}function Ir(e,t){for(Co[e]=t,e=0;e<t.length;e++)wf.add(t[e])}var Ut=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),jl=Object.prototype.hasOwnProperty,Rg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,uc={},cc={};function _g(e){return jl.call(cc,e)?!0:jl.call(uc,e)?!1:Rg.test(e)?cc[e]=!0:(uc[e]=!0,!1)}function Og(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Mg(e,t,n,r){if(t===null||typeof t>"u"||Og(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Le(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ce[e]=new Le(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ce[t]=new Le(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ce[e]=new Le(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ce[e]=new Le(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ce[e]=new Le(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ce[e]=new Le(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ce[e]=new Le(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ce[e]=new Le(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ce[e]=new Le(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ka=/[\-:]([a-z])/g;function Ya(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ka,Ya);Ce[t]=new Le(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ka,Ya);Ce[t]=new Le(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ka,Ya);Ce[t]=new Le(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ce[e]=new Le(e,1,!1,e.toLowerCase(),null,!1,!1)});Ce.xlinkHref=new Le("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ce[e]=new Le(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ga(e,t,n,r){var o=Ce.hasOwnProperty(t)?Ce[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Mg(t,n,o,r)&&(n=null),r||o===null?_g(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Kt=Ng.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ii=Symbol.for("react.element"),sr=Symbol.for("react.portal"),lr=Symbol.for("react.fragment"),Xa=Symbol.for("react.strict_mode"),Al=Symbol.for("react.profiler"),xf=Symbol.for("react.provider"),Sf=Symbol.for("react.context"),qa=Symbol.for("react.forward_ref"),Ll=Symbol.for("react.suspense"),Il=Symbol.for("react.suspense_list"),Za=Symbol.for("react.memo"),rn=Symbol.for("react.lazy"),Ef=Symbol.for("react.offscreen"),dc=Symbol.iterator;function Zr(e){return e===null||typeof e!="object"?null:(e=dc&&e[dc]||e["@@iterator"],typeof e=="function"?e:null)}var ce=Object.assign,tl;function ao(e){if(tl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);tl=t&&t[1]||""}return`
`+tl+e}var nl=!1;function rl(e,t){if(!e||nl)return"";nl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,l=i.length-1;1<=s&&0<=l&&o[s]!==i[l];)l--;for(;1<=s&&0<=l;s--,l--)if(o[s]!==i[l]){if(s!==1||l!==1)do if(s--,l--,0>l||o[s]!==i[l]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{nl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ao(e):""}function jg(e){switch(e.tag){case 5:return ao(e.type);case 16:return ao("Lazy");case 13:return ao("Suspense");case 19:return ao("SuspenseList");case 0:case 2:case 15:return e=rl(e.type,!1),e;case 11:return e=rl(e.type.render,!1),e;case 1:return e=rl(e.type,!0),e;default:return""}}function Dl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case lr:return"Fragment";case sr:return"Portal";case Al:return"Profiler";case Xa:return"StrictMode";case Ll:return"Suspense";case Il:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Sf:return(e.displayName||"Context")+".Consumer";case xf:return(e._context.displayName||"Context")+".Provider";case qa:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Za:return t=e.displayName||null,t!==null?t:Dl(e.type)||"Memo";case rn:t=e._payload,e=e._init;try{return Dl(e(t))}catch{}}return null}function Ag(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Dl(t);case 8:return t===Xa?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function kn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Cf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Lg(e){var t=Cf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function si(e){e._valueTracker||(e._valueTracker=Lg(e))}function kf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Cf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ui(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function zl(e,t){var n=t.checked;return ce({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function fc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=kn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Pf(e,t){t=t.checked,t!=null&&Ga(e,"checked",t,!1)}function Fl(e,t){Pf(e,t);var n=kn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?$l(e,t.type,n):t.hasOwnProperty("defaultValue")&&$l(e,t.type,kn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function pc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function $l(e,t,n){(t!=="number"||Ui(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var uo=Array.isArray;function yr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+kn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ul(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return ce({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function hc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(uo(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:kn(n)}}function bf(e,t){var n=kn(t.value),r=kn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function mc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Tf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Bl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Tf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var li,Nf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(li=li||document.createElement("div"),li.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=li.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ko(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var po={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ig=["Webkit","ms","Moz","O"];Object.keys(po).forEach(function(e){Ig.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),po[t]=po[e]})});function Rf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||po.hasOwnProperty(e)&&po[e]?(""+t).trim():t+"px"}function _f(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Rf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Dg=ce({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Vl(e,t){if(t){if(Dg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Hl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Wl=null;function Ja(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ql=null,wr=null,xr=null;function gc(e){if(e=Go(e)){if(typeof Ql!="function")throw Error(R(280));var t=e.stateNode;t&&(t=ks(t),Ql(e.stateNode,e.type,t))}}function Of(e){wr?xr?xr.push(e):xr=[e]:wr=e}function Mf(){if(wr){var e=wr,t=xr;if(xr=wr=null,gc(e),t)for(e=0;e<t.length;e++)gc(t[e])}}function jf(e,t){return e(t)}function Af(){}var ol=!1;function Lf(e,t,n){if(ol)return e(t,n);ol=!0;try{return jf(e,t,n)}finally{ol=!1,(wr!==null||xr!==null)&&(Af(),Mf())}}function Po(e,t){var n=e.stateNode;if(n===null)return null;var r=ks(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var Kl=!1;if(Ut)try{var Jr={};Object.defineProperty(Jr,"passive",{get:function(){Kl=!0}}),window.addEventListener("test",Jr,Jr),window.removeEventListener("test",Jr,Jr)}catch{Kl=!1}function zg(e,t,n,r,o,i,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var ho=!1,Bi=null,Vi=!1,Yl=null,Fg={onError:function(e){ho=!0,Bi=e}};function $g(e,t,n,r,o,i,s,l,a){ho=!1,Bi=null,zg.apply(Fg,arguments)}function Ug(e,t,n,r,o,i,s,l,a){if($g.apply(this,arguments),ho){if(ho){var u=Bi;ho=!1,Bi=null}else throw Error(R(198));Vi||(Vi=!0,Yl=u)}}function Jn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function If(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function vc(e){if(Jn(e)!==e)throw Error(R(188))}function Bg(e){var t=e.alternate;if(!t){if(t=Jn(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return vc(o),e;if(i===r)return vc(o),t;i=i.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function Df(e){return e=Bg(e),e!==null?zf(e):null}function zf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=zf(e);if(t!==null)return t;e=e.sibling}return null}var Ff=qe.unstable_scheduleCallback,yc=qe.unstable_cancelCallback,Vg=qe.unstable_shouldYield,Hg=qe.unstable_requestPaint,pe=qe.unstable_now,Wg=qe.unstable_getCurrentPriorityLevel,eu=qe.unstable_ImmediatePriority,$f=qe.unstable_UserBlockingPriority,Hi=qe.unstable_NormalPriority,Qg=qe.unstable_LowPriority,Uf=qe.unstable_IdlePriority,xs=null,Rt=null;function Kg(e){if(Rt&&typeof Rt.onCommitFiberRoot=="function")try{Rt.onCommitFiberRoot(xs,e,void 0,(e.current.flags&128)===128)}catch{}}var mt=Math.clz32?Math.clz32:Xg,Yg=Math.log,Gg=Math.LN2;function Xg(e){return e>>>=0,e===0?32:31-(Yg(e)/Gg|0)|0}var ai=64,ui=4194304;function co(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Wi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~o;l!==0?r=co(l):(i&=s,i!==0&&(r=co(i)))}else s=n&~o,s!==0?r=co(s):i!==0&&(r=co(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-mt(t),o=1<<n,r|=e[n],t&=~o;return r}function qg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zg(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-mt(i),l=1<<s,a=o[s];a===-1?(!(l&n)||l&r)&&(o[s]=qg(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function Gl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Bf(){var e=ai;return ai<<=1,!(ai&4194240)&&(ai=64),e}function il(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ko(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-mt(t),e[t]=n}function Jg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-mt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function tu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-mt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var J=0;function Vf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Hf,nu,Wf,Qf,Kf,Xl=!1,ci=[],gn=null,vn=null,yn=null,bo=new Map,To=new Map,sn=[],ev="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function wc(e,t){switch(e){case"focusin":case"focusout":gn=null;break;case"dragenter":case"dragleave":vn=null;break;case"mouseover":case"mouseout":yn=null;break;case"pointerover":case"pointerout":bo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":To.delete(t.pointerId)}}function eo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=Go(t),t!==null&&nu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function tv(e,t,n,r,o){switch(t){case"focusin":return gn=eo(gn,e,t,n,r,o),!0;case"dragenter":return vn=eo(vn,e,t,n,r,o),!0;case"mouseover":return yn=eo(yn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return bo.set(i,eo(bo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,To.set(i,eo(To.get(i)||null,e,t,n,r,o)),!0}return!1}function Yf(e){var t=In(e.target);if(t!==null){var n=Jn(t);if(n!==null){if(t=n.tag,t===13){if(t=If(n),t!==null){e.blockedOn=t,Kf(e.priority,function(){Wf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ni(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ql(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Wl=r,n.target.dispatchEvent(r),Wl=null}else return t=Go(n),t!==null&&nu(t),e.blockedOn=n,!1;t.shift()}return!0}function xc(e,t,n){Ni(e)&&n.delete(t)}function nv(){Xl=!1,gn!==null&&Ni(gn)&&(gn=null),vn!==null&&Ni(vn)&&(vn=null),yn!==null&&Ni(yn)&&(yn=null),bo.forEach(xc),To.forEach(xc)}function to(e,t){e.blockedOn===t&&(e.blockedOn=null,Xl||(Xl=!0,qe.unstable_scheduleCallback(qe.unstable_NormalPriority,nv)))}function No(e){function t(o){return to(o,e)}if(0<ci.length){to(ci[0],e);for(var n=1;n<ci.length;n++){var r=ci[n];r.blockedOn===e&&(r.blockedOn=null)}}for(gn!==null&&to(gn,e),vn!==null&&to(vn,e),yn!==null&&to(yn,e),bo.forEach(t),To.forEach(t),n=0;n<sn.length;n++)r=sn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<sn.length&&(n=sn[0],n.blockedOn===null);)Yf(n),n.blockedOn===null&&sn.shift()}var Sr=Kt.ReactCurrentBatchConfig,Qi=!0;function rv(e,t,n,r){var o=J,i=Sr.transition;Sr.transition=null;try{J=1,ru(e,t,n,r)}finally{J=o,Sr.transition=i}}function ov(e,t,n,r){var o=J,i=Sr.transition;Sr.transition=null;try{J=4,ru(e,t,n,r)}finally{J=o,Sr.transition=i}}function ru(e,t,n,r){if(Qi){var o=ql(e,t,n,r);if(o===null)ml(e,t,r,Ki,n),wc(e,r);else if(tv(o,e,t,n,r))r.stopPropagation();else if(wc(e,r),t&4&&-1<ev.indexOf(e)){for(;o!==null;){var i=Go(o);if(i!==null&&Hf(i),i=ql(e,t,n,r),i===null&&ml(e,t,r,Ki,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else ml(e,t,r,null,n)}}var Ki=null;function ql(e,t,n,r){if(Ki=null,e=Ja(r),e=In(e),e!==null)if(t=Jn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=If(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ki=e,null}function Gf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Wg()){case eu:return 1;case $f:return 4;case Hi:case Qg:return 16;case Uf:return 536870912;default:return 16}default:return 16}}var pn=null,ou=null,Ri=null;function Xf(){if(Ri)return Ri;var e,t=ou,n=t.length,r,o="value"in pn?pn.value:pn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return Ri=o.slice(e,1<r?1-r:void 0)}function _i(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function di(){return!0}function Sc(){return!1}function Je(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?di:Sc,this.isPropagationStopped=Sc,this}return ce(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=di)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=di)},persist:function(){},isPersistent:di}),t}var Qr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},iu=Je(Qr),Yo=ce({},Qr,{view:0,detail:0}),iv=Je(Yo),sl,ll,no,Ss=ce({},Yo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:su,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==no&&(no&&e.type==="mousemove"?(sl=e.screenX-no.screenX,ll=e.screenY-no.screenY):ll=sl=0,no=e),sl)},movementY:function(e){return"movementY"in e?e.movementY:ll}}),Ec=Je(Ss),sv=ce({},Ss,{dataTransfer:0}),lv=Je(sv),av=ce({},Yo,{relatedTarget:0}),al=Je(av),uv=ce({},Qr,{animationName:0,elapsedTime:0,pseudoElement:0}),cv=Je(uv),dv=ce({},Qr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),fv=Je(dv),pv=ce({},Qr,{data:0}),Cc=Je(pv),hv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},mv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},gv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function vv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=gv[e])?!!t[e]:!1}function su(){return vv}var yv=ce({},Yo,{key:function(e){if(e.key){var t=hv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=_i(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?mv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:su,charCode:function(e){return e.type==="keypress"?_i(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?_i(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),wv=Je(yv),xv=ce({},Ss,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),kc=Je(xv),Sv=ce({},Yo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:su}),Ev=Je(Sv),Cv=ce({},Qr,{propertyName:0,elapsedTime:0,pseudoElement:0}),kv=Je(Cv),Pv=ce({},Ss,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),bv=Je(Pv),Tv=[9,13,27,32],lu=Ut&&"CompositionEvent"in window,mo=null;Ut&&"documentMode"in document&&(mo=document.documentMode);var Nv=Ut&&"TextEvent"in window&&!mo,qf=Ut&&(!lu||mo&&8<mo&&11>=mo),Pc=" ",bc=!1;function Zf(e,t){switch(e){case"keyup":return Tv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ar=!1;function Rv(e,t){switch(e){case"compositionend":return Jf(t);case"keypress":return t.which!==32?null:(bc=!0,Pc);case"textInput":return e=t.data,e===Pc&&bc?null:e;default:return null}}function _v(e,t){if(ar)return e==="compositionend"||!lu&&Zf(e,t)?(e=Xf(),Ri=ou=pn=null,ar=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return qf&&t.locale!=="ko"?null:t.data;default:return null}}var Ov={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Tc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ov[e.type]:t==="textarea"}function ep(e,t,n,r){Of(r),t=Yi(t,"onChange"),0<t.length&&(n=new iu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var go=null,Ro=null;function Mv(e){dp(e,0)}function Es(e){var t=dr(e);if(kf(t))return e}function jv(e,t){if(e==="change")return t}var tp=!1;if(Ut){var ul;if(Ut){var cl="oninput"in document;if(!cl){var Nc=document.createElement("div");Nc.setAttribute("oninput","return;"),cl=typeof Nc.oninput=="function"}ul=cl}else ul=!1;tp=ul&&(!document.documentMode||9<document.documentMode)}function Rc(){go&&(go.detachEvent("onpropertychange",np),Ro=go=null)}function np(e){if(e.propertyName==="value"&&Es(Ro)){var t=[];ep(t,Ro,e,Ja(e)),Lf(Mv,t)}}function Av(e,t,n){e==="focusin"?(Rc(),go=t,Ro=n,go.attachEvent("onpropertychange",np)):e==="focusout"&&Rc()}function Lv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Es(Ro)}function Iv(e,t){if(e==="click")return Es(t)}function Dv(e,t){if(e==="input"||e==="change")return Es(t)}function zv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var vt=typeof Object.is=="function"?Object.is:zv;function _o(e,t){if(vt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!jl.call(t,o)||!vt(e[o],t[o]))return!1}return!0}function _c(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Oc(e,t){var n=_c(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=_c(n)}}function rp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?rp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function op(){for(var e=window,t=Ui();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ui(e.document)}return t}function au(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Fv(e){var t=op(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&rp(n.ownerDocument.documentElement,n)){if(r!==null&&au(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Oc(n,i);var s=Oc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var $v=Ut&&"documentMode"in document&&11>=document.documentMode,ur=null,Zl=null,vo=null,Jl=!1;function Mc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Jl||ur==null||ur!==Ui(r)||(r=ur,"selectionStart"in r&&au(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),vo&&_o(vo,r)||(vo=r,r=Yi(Zl,"onSelect"),0<r.length&&(t=new iu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=ur)))}function fi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var cr={animationend:fi("Animation","AnimationEnd"),animationiteration:fi("Animation","AnimationIteration"),animationstart:fi("Animation","AnimationStart"),transitionend:fi("Transition","TransitionEnd")},dl={},ip={};Ut&&(ip=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);function Cs(e){if(dl[e])return dl[e];if(!cr[e])return e;var t=cr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ip)return dl[e]=t[n];return e}var sp=Cs("animationend"),lp=Cs("animationiteration"),ap=Cs("animationstart"),up=Cs("transitionend"),cp=new Map,jc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _n(e,t){cp.set(e,t),Zn(t,[e])}for(var fl=0;fl<jc.length;fl++){var pl=jc[fl],Uv=pl.toLowerCase(),Bv=pl[0].toUpperCase()+pl.slice(1);_n(Uv,"on"+Bv)}_n(sp,"onAnimationEnd");_n(lp,"onAnimationIteration");_n(ap,"onAnimationStart");_n("dblclick","onDoubleClick");_n("focusin","onFocus");_n("focusout","onBlur");_n(up,"onTransitionEnd");Ir("onMouseEnter",["mouseout","mouseover"]);Ir("onMouseLeave",["mouseout","mouseover"]);Ir("onPointerEnter",["pointerout","pointerover"]);Ir("onPointerLeave",["pointerout","pointerover"]);Zn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Zn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Zn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Zn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Zn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Zn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var fo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Vv=new Set("cancel close invalid load scroll toggle".split(" ").concat(fo));function Ac(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Ug(r,t,void 0,e),e.currentTarget=null}function dp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;Ac(o,l,u),i=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;Ac(o,l,u),i=a}}}if(Vi)throw e=Yl,Vi=!1,Yl=null,e}function oe(e,t){var n=t[oa];n===void 0&&(n=t[oa]=new Set);var r=e+"__bubble";n.has(r)||(fp(t,e,2,!1),n.add(r))}function hl(e,t,n){var r=0;t&&(r|=4),fp(n,e,r,t)}var pi="_reactListening"+Math.random().toString(36).slice(2);function Oo(e){if(!e[pi]){e[pi]=!0,wf.forEach(function(n){n!=="selectionchange"&&(Vv.has(n)||hl(n,!1,e),hl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[pi]||(t[pi]=!0,hl("selectionchange",!1,t))}}function fp(e,t,n,r){switch(Gf(t)){case 1:var o=rv;break;case 4:o=ov;break;default:o=ru}n=o.bind(null,t,n,e),o=void 0,!Kl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function ml(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;l!==null;){if(s=In(l),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}l=l.parentNode}}r=r.return}Lf(function(){var u=i,f=Ja(n),d=[];e:{var c=cp.get(e);if(c!==void 0){var m=iu,y=e;switch(e){case"keypress":if(_i(n)===0)break e;case"keydown":case"keyup":m=wv;break;case"focusin":y="focus",m=al;break;case"focusout":y="blur",m=al;break;case"beforeblur":case"afterblur":m=al;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Ec;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=lv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Ev;break;case sp:case lp:case ap:m=cv;break;case up:m=kv;break;case"scroll":m=iv;break;case"wheel":m=bv;break;case"copy":case"cut":case"paste":m=fv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=kc}var v=(t&4)!==0,x=!v&&e==="scroll",h=v?c!==null?c+"Capture":null:c;v=[];for(var p=u,g;p!==null;){g=p;var E=g.stateNode;if(g.tag===5&&E!==null&&(g=E,h!==null&&(E=Po(p,h),E!=null&&v.push(Mo(p,E,g)))),x)break;p=p.return}0<v.length&&(c=new m(c,y,null,n,f),d.push({event:c,listeners:v}))}}if(!(t&7)){e:{if(c=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",c&&n!==Wl&&(y=n.relatedTarget||n.fromElement)&&(In(y)||y[Bt]))break e;if((m||c)&&(c=f.window===f?f:(c=f.ownerDocument)?c.defaultView||c.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=u,y=y?In(y):null,y!==null&&(x=Jn(y),y!==x||y.tag!==5&&y.tag!==6)&&(y=null)):(m=null,y=u),m!==y)){if(v=Ec,E="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=kc,E="onPointerLeave",h="onPointerEnter",p="pointer"),x=m==null?c:dr(m),g=y==null?c:dr(y),c=new v(E,p+"leave",m,n,f),c.target=x,c.relatedTarget=g,E=null,In(f)===u&&(v=new v(h,p+"enter",y,n,f),v.target=g,v.relatedTarget=x,E=v),x=E,m&&y)t:{for(v=m,h=y,p=0,g=v;g;g=ir(g))p++;for(g=0,E=h;E;E=ir(E))g++;for(;0<p-g;)v=ir(v),p--;for(;0<g-p;)h=ir(h),g--;for(;p--;){if(v===h||h!==null&&v===h.alternate)break t;v=ir(v),h=ir(h)}v=null}else v=null;m!==null&&Lc(d,c,m,v,!1),y!==null&&x!==null&&Lc(d,x,y,v,!0)}}e:{if(c=u?dr(u):window,m=c.nodeName&&c.nodeName.toLowerCase(),m==="select"||m==="input"&&c.type==="file")var C=jv;else if(Tc(c))if(tp)C=Dv;else{C=Lv;var b=Av}else(m=c.nodeName)&&m.toLowerCase()==="input"&&(c.type==="checkbox"||c.type==="radio")&&(C=Iv);if(C&&(C=C(e,u))){ep(d,C,n,f);break e}b&&b(e,c,u),e==="focusout"&&(b=c._wrapperState)&&b.controlled&&c.type==="number"&&$l(c,"number",c.value)}switch(b=u?dr(u):window,e){case"focusin":(Tc(b)||b.contentEditable==="true")&&(ur=b,Zl=u,vo=null);break;case"focusout":vo=Zl=ur=null;break;case"mousedown":Jl=!0;break;case"contextmenu":case"mouseup":case"dragend":Jl=!1,Mc(d,n,f);break;case"selectionchange":if($v)break;case"keydown":case"keyup":Mc(d,n,f)}var T;if(lu)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else ar?Zf(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(qf&&n.locale!=="ko"&&(ar||P!=="onCompositionStart"?P==="onCompositionEnd"&&ar&&(T=Xf()):(pn=f,ou="value"in pn?pn.value:pn.textContent,ar=!0)),b=Yi(u,P),0<b.length&&(P=new Cc(P,e,null,n,f),d.push({event:P,listeners:b}),T?P.data=T:(T=Jf(n),T!==null&&(P.data=T)))),(T=Nv?Rv(e,n):_v(e,n))&&(u=Yi(u,"onBeforeInput"),0<u.length&&(f=new Cc("onBeforeInput","beforeinput",null,n,f),d.push({event:f,listeners:u}),f.data=T))}dp(d,t)})}function Mo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Yi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Po(e,n),i!=null&&r.unshift(Mo(e,i,o)),i=Po(e,t),i!=null&&r.push(Mo(e,i,o))),e=e.return}return r}function ir(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Lc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=Po(n,i),a!=null&&s.unshift(Mo(n,a,l))):o||(a=Po(n,i),a!=null&&s.push(Mo(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Hv=/\r\n?/g,Wv=/\u0000|\uFFFD/g;function Ic(e){return(typeof e=="string"?e:""+e).replace(Hv,`
`).replace(Wv,"")}function hi(e,t,n){if(t=Ic(t),Ic(e)!==t&&n)throw Error(R(425))}function Gi(){}var ea=null,ta=null;function na(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ra=typeof setTimeout=="function"?setTimeout:void 0,Qv=typeof clearTimeout=="function"?clearTimeout:void 0,Dc=typeof Promise=="function"?Promise:void 0,Kv=typeof queueMicrotask=="function"?queueMicrotask:typeof Dc<"u"?function(e){return Dc.resolve(null).then(e).catch(Yv)}:ra;function Yv(e){setTimeout(function(){throw e})}function gl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),No(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);No(t)}function wn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function zc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Kr=Math.random().toString(36).slice(2),Nt="__reactFiber$"+Kr,jo="__reactProps$"+Kr,Bt="__reactContainer$"+Kr,oa="__reactEvents$"+Kr,Gv="__reactListeners$"+Kr,Xv="__reactHandles$"+Kr;function In(e){var t=e[Nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Bt]||n[Nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=zc(e);e!==null;){if(n=e[Nt])return n;e=zc(e)}return t}e=n,n=e.parentNode}return null}function Go(e){return e=e[Nt]||e[Bt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function dr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function ks(e){return e[jo]||null}var ia=[],fr=-1;function On(e){return{current:e}}function ie(e){0>fr||(e.current=ia[fr],ia[fr]=null,fr--)}function te(e,t){fr++,ia[fr]=e.current,e.current=t}var Pn={},Re=On(Pn),ze=On(!1),Wn=Pn;function Dr(e,t){var n=e.type.contextTypes;if(!n)return Pn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Fe(e){return e=e.childContextTypes,e!=null}function Xi(){ie(ze),ie(Re)}function Fc(e,t,n){if(Re.current!==Pn)throw Error(R(168));te(Re,t),te(ze,n)}function pp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,Ag(e)||"Unknown",o));return ce({},n,r)}function qi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Pn,Wn=Re.current,te(Re,e),te(ze,ze.current),!0}function $c(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=pp(e,t,Wn),r.__reactInternalMemoizedMergedChildContext=e,ie(ze),ie(Re),te(Re,e)):ie(ze),te(ze,n)}var Dt=null,Ps=!1,vl=!1;function hp(e){Dt===null?Dt=[e]:Dt.push(e)}function qv(e){Ps=!0,hp(e)}function Mn(){if(!vl&&Dt!==null){vl=!0;var e=0,t=J;try{var n=Dt;for(J=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Dt=null,Ps=!1}catch(o){throw Dt!==null&&(Dt=Dt.slice(e+1)),Ff(eu,Mn),o}finally{J=t,vl=!1}}return null}var pr=[],hr=0,Zi=null,Ji=0,nt=[],rt=0,Qn=null,zt=1,Ft="";function An(e,t){pr[hr++]=Ji,pr[hr++]=Zi,Zi=e,Ji=t}function mp(e,t,n){nt[rt++]=zt,nt[rt++]=Ft,nt[rt++]=Qn,Qn=e;var r=zt;e=Ft;var o=32-mt(r)-1;r&=~(1<<o),n+=1;var i=32-mt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,zt=1<<32-mt(t)+o|n<<o|r,Ft=i+e}else zt=1<<i|n<<o|r,Ft=e}function uu(e){e.return!==null&&(An(e,1),mp(e,1,0))}function cu(e){for(;e===Zi;)Zi=pr[--hr],pr[hr]=null,Ji=pr[--hr],pr[hr]=null;for(;e===Qn;)Qn=nt[--rt],nt[rt]=null,Ft=nt[--rt],nt[rt]=null,zt=nt[--rt],nt[rt]=null}var Ge=null,Ye=null,se=!1,ht=null;function gp(e,t){var n=ot(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Uc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ge=e,Ye=wn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ge=e,Ye=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Qn!==null?{id:zt,overflow:Ft}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ot(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ge=e,Ye=null,!0):!1;default:return!1}}function sa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function la(e){if(se){var t=Ye;if(t){var n=t;if(!Uc(e,t)){if(sa(e))throw Error(R(418));t=wn(n.nextSibling);var r=Ge;t&&Uc(e,t)?gp(r,n):(e.flags=e.flags&-4097|2,se=!1,Ge=e)}}else{if(sa(e))throw Error(R(418));e.flags=e.flags&-4097|2,se=!1,Ge=e}}}function Bc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ge=e}function mi(e){if(e!==Ge)return!1;if(!se)return Bc(e),se=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!na(e.type,e.memoizedProps)),t&&(t=Ye)){if(sa(e))throw vp(),Error(R(418));for(;t;)gp(e,t),t=wn(t.nextSibling)}if(Bc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ye=wn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ye=null}}else Ye=Ge?wn(e.stateNode.nextSibling):null;return!0}function vp(){for(var e=Ye;e;)e=wn(e.nextSibling)}function zr(){Ye=Ge=null,se=!1}function du(e){ht===null?ht=[e]:ht.push(e)}var Zv=Kt.ReactCurrentBatchConfig;function ro(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var l=o.refs;s===null?delete l[i]:l[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function gi(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Vc(e){var t=e._init;return t(e._payload)}function yp(e){function t(h,p){if(e){var g=h.deletions;g===null?(h.deletions=[p],h.flags|=16):g.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=Cn(h,p),h.index=0,h.sibling=null,h}function i(h,p,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<p?(h.flags|=2,p):g):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function l(h,p,g,E){return p===null||p.tag!==6?(p=kl(g,h.mode,E),p.return=h,p):(p=o(p,g),p.return=h,p)}function a(h,p,g,E){var C=g.type;return C===lr?f(h,p,g.props.children,E,g.key):p!==null&&(p.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===rn&&Vc(C)===p.type)?(E=o(p,g.props),E.ref=ro(h,p,g),E.return=h,E):(E=Di(g.type,g.key,g.props,null,h.mode,E),E.ref=ro(h,p,g),E.return=h,E)}function u(h,p,g,E){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=Pl(g,h.mode,E),p.return=h,p):(p=o(p,g.children||[]),p.return=h,p)}function f(h,p,g,E,C){return p===null||p.tag!==7?(p=Hn(g,h.mode,E,C),p.return=h,p):(p=o(p,g),p.return=h,p)}function d(h,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=kl(""+p,h.mode,g),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ii:return g=Di(p.type,p.key,p.props,null,h.mode,g),g.ref=ro(h,null,p),g.return=h,g;case sr:return p=Pl(p,h.mode,g),p.return=h,p;case rn:var E=p._init;return d(h,E(p._payload),g)}if(uo(p)||Zr(p))return p=Hn(p,h.mode,g,null),p.return=h,p;gi(h,p)}return null}function c(h,p,g,E){var C=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return C!==null?null:l(h,p,""+g,E);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case ii:return g.key===C?a(h,p,g,E):null;case sr:return g.key===C?u(h,p,g,E):null;case rn:return C=g._init,c(h,p,C(g._payload),E)}if(uo(g)||Zr(g))return C!==null?null:f(h,p,g,E,null);gi(h,g)}return null}function m(h,p,g,E,C){if(typeof E=="string"&&E!==""||typeof E=="number")return h=h.get(g)||null,l(p,h,""+E,C);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case ii:return h=h.get(E.key===null?g:E.key)||null,a(p,h,E,C);case sr:return h=h.get(E.key===null?g:E.key)||null,u(p,h,E,C);case rn:var b=E._init;return m(h,p,g,b(E._payload),C)}if(uo(E)||Zr(E))return h=h.get(g)||null,f(p,h,E,C,null);gi(p,E)}return null}function y(h,p,g,E){for(var C=null,b=null,T=p,P=p=0,j=null;T!==null&&P<g.length;P++){T.index>P?(j=T,T=null):j=T.sibling;var M=c(h,T,g[P],E);if(M===null){T===null&&(T=j);break}e&&T&&M.alternate===null&&t(h,T),p=i(M,p,P),b===null?C=M:b.sibling=M,b=M,T=j}if(P===g.length)return n(h,T),se&&An(h,P),C;if(T===null){for(;P<g.length;P++)T=d(h,g[P],E),T!==null&&(p=i(T,p,P),b===null?C=T:b.sibling=T,b=T);return se&&An(h,P),C}for(T=r(h,T);P<g.length;P++)j=m(T,h,P,g[P],E),j!==null&&(e&&j.alternate!==null&&T.delete(j.key===null?P:j.key),p=i(j,p,P),b===null?C=j:b.sibling=j,b=j);return e&&T.forEach(function(F){return t(h,F)}),se&&An(h,P),C}function v(h,p,g,E){var C=Zr(g);if(typeof C!="function")throw Error(R(150));if(g=C.call(g),g==null)throw Error(R(151));for(var b=C=null,T=p,P=p=0,j=null,M=g.next();T!==null&&!M.done;P++,M=g.next()){T.index>P?(j=T,T=null):j=T.sibling;var F=c(h,T,M.value,E);if(F===null){T===null&&(T=j);break}e&&T&&F.alternate===null&&t(h,T),p=i(F,p,P),b===null?C=F:b.sibling=F,b=F,T=j}if(M.done)return n(h,T),se&&An(h,P),C;if(T===null){for(;!M.done;P++,M=g.next())M=d(h,M.value,E),M!==null&&(p=i(M,p,P),b===null?C=M:b.sibling=M,b=M);return se&&An(h,P),C}for(T=r(h,T);!M.done;P++,M=g.next())M=m(T,h,P,M.value,E),M!==null&&(e&&M.alternate!==null&&T.delete(M.key===null?P:M.key),p=i(M,p,P),b===null?C=M:b.sibling=M,b=M);return e&&T.forEach(function(L){return t(h,L)}),se&&An(h,P),C}function x(h,p,g,E){if(typeof g=="object"&&g!==null&&g.type===lr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case ii:e:{for(var C=g.key,b=p;b!==null;){if(b.key===C){if(C=g.type,C===lr){if(b.tag===7){n(h,b.sibling),p=o(b,g.props.children),p.return=h,h=p;break e}}else if(b.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===rn&&Vc(C)===b.type){n(h,b.sibling),p=o(b,g.props),p.ref=ro(h,b,g),p.return=h,h=p;break e}n(h,b);break}else t(h,b);b=b.sibling}g.type===lr?(p=Hn(g.props.children,h.mode,E,g.key),p.return=h,h=p):(E=Di(g.type,g.key,g.props,null,h.mode,E),E.ref=ro(h,p,g),E.return=h,h=E)}return s(h);case sr:e:{for(b=g.key;p!==null;){if(p.key===b)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(h,p.sibling),p=o(p,g.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=Pl(g,h.mode,E),p.return=h,h=p}return s(h);case rn:return b=g._init,x(h,p,b(g._payload),E)}if(uo(g))return y(h,p,g,E);if(Zr(g))return v(h,p,g,E);gi(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,g),p.return=h,h=p):(n(h,p),p=kl(g,h.mode,E),p.return=h,h=p),s(h)):n(h,p)}return x}var Fr=yp(!0),wp=yp(!1),es=On(null),ts=null,mr=null,fu=null;function pu(){fu=mr=ts=null}function hu(e){var t=es.current;ie(es),e._currentValue=t}function aa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Er(e,t){ts=e,fu=mr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(De=!0),e.firstContext=null)}function st(e){var t=e._currentValue;if(fu!==e)if(e={context:e,memoizedValue:t,next:null},mr===null){if(ts===null)throw Error(R(308));mr=e,ts.dependencies={lanes:0,firstContext:e}}else mr=mr.next=e;return t}var Dn=null;function mu(e){Dn===null?Dn=[e]:Dn.push(e)}function xp(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,mu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Vt(e,r)}function Vt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var on=!1;function gu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Sp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function $t(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function xn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,X&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Vt(e,n)}return o=r.interleaved,o===null?(t.next=t,mu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Vt(e,n)}function Oi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,tu(e,n)}}function Hc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ns(e,t,n,r){var o=e.updateQueue;on=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?i=u:s.next=u,s=a;var f=e.alternate;f!==null&&(f=f.updateQueue,l=f.lastBaseUpdate,l!==s&&(l===null?f.firstBaseUpdate=u:l.next=u,f.lastBaseUpdate=a))}if(i!==null){var d=o.baseState;s=0,f=u=a=null,l=i;do{var c=l.lane,m=l.eventTime;if((r&c)===c){f!==null&&(f=f.next={eventTime:m,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var y=e,v=l;switch(c=t,m=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){d=y.call(m,d,c);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,c=typeof y=="function"?y.call(m,d,c):y,c==null)break e;d=ce({},d,c);break e;case 2:on=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,c=o.effects,c===null?o.effects=[l]:c.push(l))}else m={eventTime:m,lane:c,tag:l.tag,payload:l.payload,callback:l.callback,next:null},f===null?(u=f=m,a=d):f=f.next=m,s|=c;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;c=l,l=c.next,c.next=null,o.lastBaseUpdate=c,o.shared.pending=null}}while(!0);if(f===null&&(a=d),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Yn|=s,e.lanes=s,e.memoizedState=d}}function Wc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var Xo={},_t=On(Xo),Ao=On(Xo),Lo=On(Xo);function zn(e){if(e===Xo)throw Error(R(174));return e}function vu(e,t){switch(te(Lo,t),te(Ao,e),te(_t,Xo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Bl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Bl(t,e)}ie(_t),te(_t,t)}function $r(){ie(_t),ie(Ao),ie(Lo)}function Ep(e){zn(Lo.current);var t=zn(_t.current),n=Bl(t,e.type);t!==n&&(te(Ao,e),te(_t,n))}function yu(e){Ao.current===e&&(ie(_t),ie(Ao))}var ae=On(0);function rs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var yl=[];function wu(){for(var e=0;e<yl.length;e++)yl[e]._workInProgressVersionPrimary=null;yl.length=0}var Mi=Kt.ReactCurrentDispatcher,wl=Kt.ReactCurrentBatchConfig,Kn=0,ue=null,me=null,ye=null,os=!1,yo=!1,Io=0,Jv=0;function Pe(){throw Error(R(321))}function xu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!vt(e[n],t[n]))return!1;return!0}function Su(e,t,n,r,o,i){if(Kn=i,ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Mi.current=e===null||e.memoizedState===null?r0:o0,e=n(r,o),yo){i=0;do{if(yo=!1,Io=0,25<=i)throw Error(R(301));i+=1,ye=me=null,t.updateQueue=null,Mi.current=i0,e=n(r,o)}while(yo)}if(Mi.current=is,t=me!==null&&me.next!==null,Kn=0,ye=me=ue=null,os=!1,t)throw Error(R(300));return e}function Eu(){var e=Io!==0;return Io=0,e}function kt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ye===null?ue.memoizedState=ye=e:ye=ye.next=e,ye}function lt(){if(me===null){var e=ue.alternate;e=e!==null?e.memoizedState:null}else e=me.next;var t=ye===null?ue.memoizedState:ye.next;if(t!==null)ye=t,me=e;else{if(e===null)throw Error(R(310));me=e,e={memoizedState:me.memoizedState,baseState:me.baseState,baseQueue:me.baseQueue,queue:me.queue,next:null},ye===null?ue.memoizedState=ye=e:ye=ye.next=e}return ye}function Do(e,t){return typeof t=="function"?t(e):t}function xl(e){var t=lt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=me,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=s=null,a=null,u=i;do{var f=u.lane;if((Kn&f)===f)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=d,s=r):a=a.next=d,ue.lanes|=f,Yn|=f}u=u.next}while(u!==null&&u!==i);a===null?s=r:a.next=l,vt(r,t.memoizedState)||(De=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ue.lanes|=i,Yn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Sl(e){var t=lt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);vt(i,t.memoizedState)||(De=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Cp(){}function kp(e,t){var n=ue,r=lt(),o=t(),i=!vt(r.memoizedState,o);if(i&&(r.memoizedState=o,De=!0),r=r.queue,Cu(Tp.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ye!==null&&ye.memoizedState.tag&1){if(n.flags|=2048,zo(9,bp.bind(null,n,r,o,t),void 0,null),we===null)throw Error(R(349));Kn&30||Pp(n,t,o)}return o}function Pp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function bp(e,t,n,r){t.value=n,t.getSnapshot=r,Np(t)&&Rp(e)}function Tp(e,t,n){return n(function(){Np(t)&&Rp(e)})}function Np(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!vt(e,n)}catch{return!0}}function Rp(e){var t=Vt(e,1);t!==null&&gt(t,e,1,-1)}function Qc(e){var t=kt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Do,lastRenderedState:e},t.queue=e,e=e.dispatch=n0.bind(null,ue,e),[t.memoizedState,e]}function zo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function _p(){return lt().memoizedState}function ji(e,t,n,r){var o=kt();ue.flags|=e,o.memoizedState=zo(1|t,n,void 0,r===void 0?null:r)}function bs(e,t,n,r){var o=lt();r=r===void 0?null:r;var i=void 0;if(me!==null){var s=me.memoizedState;if(i=s.destroy,r!==null&&xu(r,s.deps)){o.memoizedState=zo(t,n,i,r);return}}ue.flags|=e,o.memoizedState=zo(1|t,n,i,r)}function Kc(e,t){return ji(8390656,8,e,t)}function Cu(e,t){return bs(2048,8,e,t)}function Op(e,t){return bs(4,2,e,t)}function Mp(e,t){return bs(4,4,e,t)}function jp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ap(e,t,n){return n=n!=null?n.concat([e]):null,bs(4,4,jp.bind(null,t,e),n)}function ku(){}function Lp(e,t){var n=lt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&xu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ip(e,t){var n=lt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&xu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Dp(e,t,n){return Kn&21?(vt(n,t)||(n=Bf(),ue.lanes|=n,Yn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,De=!0),e.memoizedState=n)}function e0(e,t){var n=J;J=n!==0&&4>n?n:4,e(!0);var r=wl.transition;wl.transition={};try{e(!1),t()}finally{J=n,wl.transition=r}}function zp(){return lt().memoizedState}function t0(e,t,n){var r=En(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Fp(e))$p(t,n);else if(n=xp(e,t,n,r),n!==null){var o=je();gt(n,e,r,o),Up(n,t,r)}}function n0(e,t,n){var r=En(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Fp(e))$p(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,vt(l,s)){var a=t.interleaved;a===null?(o.next=o,mu(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=xp(e,t,o,r),n!==null&&(o=je(),gt(n,e,r,o),Up(n,t,r))}}function Fp(e){var t=e.alternate;return e===ue||t!==null&&t===ue}function $p(e,t){yo=os=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Up(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,tu(e,n)}}var is={readContext:st,useCallback:Pe,useContext:Pe,useEffect:Pe,useImperativeHandle:Pe,useInsertionEffect:Pe,useLayoutEffect:Pe,useMemo:Pe,useReducer:Pe,useRef:Pe,useState:Pe,useDebugValue:Pe,useDeferredValue:Pe,useTransition:Pe,useMutableSource:Pe,useSyncExternalStore:Pe,useId:Pe,unstable_isNewReconciler:!1},r0={readContext:st,useCallback:function(e,t){return kt().memoizedState=[e,t===void 0?null:t],e},useContext:st,useEffect:Kc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ji(4194308,4,jp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ji(4194308,4,e,t)},useInsertionEffect:function(e,t){return ji(4,2,e,t)},useMemo:function(e,t){var n=kt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=kt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=t0.bind(null,ue,e),[r.memoizedState,e]},useRef:function(e){var t=kt();return e={current:e},t.memoizedState=e},useState:Qc,useDebugValue:ku,useDeferredValue:function(e){return kt().memoizedState=e},useTransition:function(){var e=Qc(!1),t=e[0];return e=e0.bind(null,e[1]),kt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ue,o=kt();if(se){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),we===null)throw Error(R(349));Kn&30||Pp(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Kc(Tp.bind(null,r,i,e),[e]),r.flags|=2048,zo(9,bp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=kt(),t=we.identifierPrefix;if(se){var n=Ft,r=zt;n=(r&~(1<<32-mt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Io++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Jv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},o0={readContext:st,useCallback:Lp,useContext:st,useEffect:Cu,useImperativeHandle:Ap,useInsertionEffect:Op,useLayoutEffect:Mp,useMemo:Ip,useReducer:xl,useRef:_p,useState:function(){return xl(Do)},useDebugValue:ku,useDeferredValue:function(e){var t=lt();return Dp(t,me.memoizedState,e)},useTransition:function(){var e=xl(Do)[0],t=lt().memoizedState;return[e,t]},useMutableSource:Cp,useSyncExternalStore:kp,useId:zp,unstable_isNewReconciler:!1},i0={readContext:st,useCallback:Lp,useContext:st,useEffect:Cu,useImperativeHandle:Ap,useInsertionEffect:Op,useLayoutEffect:Mp,useMemo:Ip,useReducer:Sl,useRef:_p,useState:function(){return Sl(Do)},useDebugValue:ku,useDeferredValue:function(e){var t=lt();return me===null?t.memoizedState=e:Dp(t,me.memoizedState,e)},useTransition:function(){var e=Sl(Do)[0],t=lt().memoizedState;return[e,t]},useMutableSource:Cp,useSyncExternalStore:kp,useId:zp,unstable_isNewReconciler:!1};function ct(e,t){if(e&&e.defaultProps){t=ce({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ua(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ce({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ts={isMounted:function(e){return(e=e._reactInternals)?Jn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=je(),o=En(e),i=$t(r,o);i.payload=t,n!=null&&(i.callback=n),t=xn(e,i,o),t!==null&&(gt(t,e,o,r),Oi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=je(),o=En(e),i=$t(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=xn(e,i,o),t!==null&&(gt(t,e,o,r),Oi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=je(),r=En(e),o=$t(n,r);o.tag=2,t!=null&&(o.callback=t),t=xn(e,o,r),t!==null&&(gt(t,e,r,n),Oi(t,e,r))}};function Yc(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!_o(n,r)||!_o(o,i):!0}function Bp(e,t,n){var r=!1,o=Pn,i=t.contextType;return typeof i=="object"&&i!==null?i=st(i):(o=Fe(t)?Wn:Re.current,r=t.contextTypes,i=(r=r!=null)?Dr(e,o):Pn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ts,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Gc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ts.enqueueReplaceState(t,t.state,null)}function ca(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},gu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=st(i):(i=Fe(t)?Wn:Re.current,o.context=Dr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ua(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Ts.enqueueReplaceState(o,o.state,null),ns(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Ur(e,t){try{var n="",r=t;do n+=jg(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function El(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function da(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var s0=typeof WeakMap=="function"?WeakMap:Map;function Vp(e,t,n){n=$t(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ls||(ls=!0,Sa=r),da(e,t)},n}function Hp(e,t,n){n=$t(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){da(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){da(e,t),typeof r!="function"&&(Sn===null?Sn=new Set([this]):Sn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Xc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new s0;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=x0.bind(null,e,t,n),t.then(e,e))}function qc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Zc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=$t(-1,1),t.tag=2,xn(n,t,1))),n.lanes|=1),e)}var l0=Kt.ReactCurrentOwner,De=!1;function Oe(e,t,n,r){t.child=e===null?wp(t,null,n,r):Fr(t,e.child,n,r)}function Jc(e,t,n,r,o){n=n.render;var i=t.ref;return Er(t,o),r=Su(e,t,n,r,i,o),n=Eu(),e!==null&&!De?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ht(e,t,o)):(se&&n&&uu(t),t.flags|=1,Oe(e,t,r,o),t.child)}function ed(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Mu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Wp(e,t,i,r,o)):(e=Di(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:_o,n(s,r)&&e.ref===t.ref)return Ht(e,t,o)}return t.flags|=1,e=Cn(i,r),e.ref=t.ref,e.return=t,t.child=e}function Wp(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(_o(i,r)&&e.ref===t.ref)if(De=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(De=!0);else return t.lanes=e.lanes,Ht(e,t,o)}return fa(e,t,n,r,o)}function Qp(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},te(vr,Qe),Qe|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,te(vr,Qe),Qe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,te(vr,Qe),Qe|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,te(vr,Qe),Qe|=r;return Oe(e,t,o,n),t.child}function Kp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function fa(e,t,n,r,o){var i=Fe(n)?Wn:Re.current;return i=Dr(t,i),Er(t,o),n=Su(e,t,n,r,i,o),r=Eu(),e!==null&&!De?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ht(e,t,o)):(se&&r&&uu(t),t.flags|=1,Oe(e,t,n,o),t.child)}function td(e,t,n,r,o){if(Fe(n)){var i=!0;qi(t)}else i=!1;if(Er(t,o),t.stateNode===null)Ai(e,t),Bp(t,n,r),ca(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=st(u):(u=Fe(n)?Wn:Re.current,u=Dr(t,u));var f=n.getDerivedStateFromProps,d=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Gc(t,s,r,u),on=!1;var c=t.memoizedState;s.state=c,ns(t,r,s,o),a=t.memoizedState,l!==r||c!==a||ze.current||on?(typeof f=="function"&&(ua(t,n,f,r),a=t.memoizedState),(l=on||Yc(t,n,l,r,c,a,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Sp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:ct(t.type,l),s.props=u,d=t.pendingProps,c=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=st(a):(a=Fe(n)?Wn:Re.current,a=Dr(t,a));var m=n.getDerivedStateFromProps;(f=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==d||c!==a)&&Gc(t,s,r,a),on=!1,c=t.memoizedState,s.state=c,ns(t,r,s,o);var y=t.memoizedState;l!==d||c!==y||ze.current||on?(typeof m=="function"&&(ua(t,n,m,r),y=t.memoizedState),(u=on||Yc(t,n,u,r,c,y,a)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),r=!1)}return pa(e,t,n,r,i,o)}function pa(e,t,n,r,o,i){Kp(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&$c(t,n,!1),Ht(e,t,i);r=t.stateNode,l0.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Fr(t,e.child,null,i),t.child=Fr(t,null,l,i)):Oe(e,t,l,i),t.memoizedState=r.state,o&&$c(t,n,!0),t.child}function Yp(e){var t=e.stateNode;t.pendingContext?Fc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Fc(e,t.context,!1),vu(e,t.containerInfo)}function nd(e,t,n,r,o){return zr(),du(o),t.flags|=256,Oe(e,t,n,r),t.child}var ha={dehydrated:null,treeContext:null,retryLane:0};function ma(e){return{baseLanes:e,cachePool:null,transitions:null}}function Gp(e,t,n){var r=t.pendingProps,o=ae.current,i=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),te(ae,o&1),e===null)return la(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=_s(s,r,0,null),e=Hn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ma(n),t.memoizedState=ha,e):Pu(t,s));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return a0(e,t,s,r,l,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Cn(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=Cn(l,i):(i=Hn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?ma(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=ha,r}return i=e.child,e=i.sibling,r=Cn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Pu(e,t){return t=_s({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function vi(e,t,n,r){return r!==null&&du(r),Fr(t,e.child,null,n),e=Pu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function a0(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=El(Error(R(422))),vi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=_s({mode:"visible",children:r.children},o,0,null),i=Hn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Fr(t,e.child,null,s),t.child.memoizedState=ma(s),t.memoizedState=ha,i);if(!(t.mode&1))return vi(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(R(419)),r=El(i,r,void 0),vi(e,t,s,r)}if(l=(s&e.childLanes)!==0,De||l){if(r=we,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Vt(e,o),gt(r,e,o,-1))}return Ou(),r=El(Error(R(421))),vi(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=S0.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ye=wn(o.nextSibling),Ge=t,se=!0,ht=null,e!==null&&(nt[rt++]=zt,nt[rt++]=Ft,nt[rt++]=Qn,zt=e.id,Ft=e.overflow,Qn=t),t=Pu(t,r.children),t.flags|=4096,t)}function rd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),aa(e.return,t,n)}function Cl(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Xp(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Oe(e,t,r.children,n),r=ae.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&rd(e,n,t);else if(e.tag===19)rd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(te(ae,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&rs(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Cl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&rs(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Cl(t,!0,n,null,i);break;case"together":Cl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ai(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ht(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Yn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=Cn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Cn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function u0(e,t,n){switch(t.tag){case 3:Yp(t),zr();break;case 5:Ep(t);break;case 1:Fe(t.type)&&qi(t);break;case 4:vu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;te(es,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(te(ae,ae.current&1),t.flags|=128,null):n&t.child.childLanes?Gp(e,t,n):(te(ae,ae.current&1),e=Ht(e,t,n),e!==null?e.sibling:null);te(ae,ae.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Xp(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),te(ae,ae.current),r)break;return null;case 22:case 23:return t.lanes=0,Qp(e,t,n)}return Ht(e,t,n)}var qp,ga,Zp,Jp;qp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ga=function(){};Zp=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,zn(_t.current);var i=null;switch(n){case"input":o=zl(e,o),r=zl(e,r),i=[];break;case"select":o=ce({},o,{value:void 0}),r=ce({},r,{value:void 0}),i=[];break;case"textarea":o=Ul(e,o),r=Ul(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Gi)}Vl(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Co.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Co.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&oe("scroll",e),i||l===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Jp=function(e,t,n,r){n!==r&&(t.flags|=4)};function oo(e,t){if(!se)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function be(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function c0(e,t,n){var r=t.pendingProps;switch(cu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return be(t),null;case 1:return Fe(t.type)&&Xi(),be(t),null;case 3:return r=t.stateNode,$r(),ie(ze),ie(Re),wu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(mi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ht!==null&&(ka(ht),ht=null))),ga(e,t),be(t),null;case 5:yu(t);var o=zn(Lo.current);if(n=t.type,e!==null&&t.stateNode!=null)Zp(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return be(t),null}if(e=zn(_t.current),mi(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Nt]=t,r[jo]=i,e=(t.mode&1)!==0,n){case"dialog":oe("cancel",r),oe("close",r);break;case"iframe":case"object":case"embed":oe("load",r);break;case"video":case"audio":for(o=0;o<fo.length;o++)oe(fo[o],r);break;case"source":oe("error",r);break;case"img":case"image":case"link":oe("error",r),oe("load",r);break;case"details":oe("toggle",r);break;case"input":fc(r,i),oe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},oe("invalid",r);break;case"textarea":hc(r,i),oe("invalid",r)}Vl(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var l=i[s];s==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&hi(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&hi(r.textContent,l,e),o=["children",""+l]):Co.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&oe("scroll",r)}switch(n){case"input":si(r),pc(r,i,!0);break;case"textarea":si(r),mc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Gi)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Tf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Nt]=t,e[jo]=r,qp(e,t,!1,!1),t.stateNode=e;e:{switch(s=Hl(n,r),n){case"dialog":oe("cancel",e),oe("close",e),o=r;break;case"iframe":case"object":case"embed":oe("load",e),o=r;break;case"video":case"audio":for(o=0;o<fo.length;o++)oe(fo[o],e);o=r;break;case"source":oe("error",e),o=r;break;case"img":case"image":case"link":oe("error",e),oe("load",e),o=r;break;case"details":oe("toggle",e),o=r;break;case"input":fc(e,r),o=zl(e,r),oe("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ce({},r,{value:void 0}),oe("invalid",e);break;case"textarea":hc(e,r),o=Ul(e,r),oe("invalid",e);break;default:o=r}Vl(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?_f(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Nf(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&ko(e,a):typeof a=="number"&&ko(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Co.hasOwnProperty(i)?a!=null&&i==="onScroll"&&oe("scroll",e):a!=null&&Ga(e,i,a,s))}switch(n){case"input":si(e),pc(e,r,!1);break;case"textarea":si(e),mc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+kn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?yr(e,!!r.multiple,i,!1):r.defaultValue!=null&&yr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Gi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return be(t),null;case 6:if(e&&t.stateNode!=null)Jp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=zn(Lo.current),zn(_t.current),mi(t)){if(r=t.stateNode,n=t.memoizedProps,r[Nt]=t,(i=r.nodeValue!==n)&&(e=Ge,e!==null))switch(e.tag){case 3:hi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&hi(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Nt]=t,t.stateNode=r}return be(t),null;case 13:if(ie(ae),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(se&&Ye!==null&&t.mode&1&&!(t.flags&128))vp(),zr(),t.flags|=98560,i=!1;else if(i=mi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(R(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(R(317));i[Nt]=t}else zr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;be(t),i=!1}else ht!==null&&(ka(ht),ht=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ae.current&1?ve===0&&(ve=3):Ou())),t.updateQueue!==null&&(t.flags|=4),be(t),null);case 4:return $r(),ga(e,t),e===null&&Oo(t.stateNode.containerInfo),be(t),null;case 10:return hu(t.type._context),be(t),null;case 17:return Fe(t.type)&&Xi(),be(t),null;case 19:if(ie(ae),i=t.memoizedState,i===null)return be(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)oo(i,!1);else{if(ve!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=rs(e),s!==null){for(t.flags|=128,oo(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return te(ae,ae.current&1|2),t.child}e=e.sibling}i.tail!==null&&pe()>Br&&(t.flags|=128,r=!0,oo(i,!1),t.lanes=4194304)}else{if(!r)if(e=rs(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),oo(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!se)return be(t),null}else 2*pe()-i.renderingStartTime>Br&&n!==1073741824&&(t.flags|=128,r=!0,oo(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=pe(),t.sibling=null,n=ae.current,te(ae,r?n&1|2:n&1),t):(be(t),null);case 22:case 23:return _u(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Qe&1073741824&&(be(t),t.subtreeFlags&6&&(t.flags|=8192)):be(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function d0(e,t){switch(cu(t),t.tag){case 1:return Fe(t.type)&&Xi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return $r(),ie(ze),ie(Re),wu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return yu(t),null;case 13:if(ie(ae),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));zr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ie(ae),null;case 4:return $r(),null;case 10:return hu(t.type._context),null;case 22:case 23:return _u(),null;case 24:return null;default:return null}}var yi=!1,Ne=!1,f0=typeof WeakSet=="function"?WeakSet:Set,I=null;function gr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){fe(e,t,r)}else n.current=null}function va(e,t,n){try{n()}catch(r){fe(e,t,r)}}var od=!1;function p0(e,t){if(ea=Qi,e=op(),au(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,f=0,d=e,c=null;t:for(;;){for(var m;d!==n||o!==0&&d.nodeType!==3||(l=s+o),d!==i||r!==0&&d.nodeType!==3||(a=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(m=d.firstChild)!==null;)c=d,d=m;for(;;){if(d===e)break t;if(c===n&&++u===o&&(l=s),c===i&&++f===r&&(a=s),(m=d.nextSibling)!==null)break;d=c,c=d.parentNode}d=m}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Qi=!1,I=t;I!==null;)if(t=I,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,I=e;else for(;I!==null;){t=I;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,x=y.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?v:ct(t.type,v),x);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(E){fe(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,I=e;break}I=t.return}return y=od,od=!1,y}function wo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&va(t,n,i)}o=o.next}while(o!==r)}}function Ns(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ya(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function eh(e){var t=e.alternate;t!==null&&(e.alternate=null,eh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Nt],delete t[jo],delete t[oa],delete t[Gv],delete t[Xv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function th(e){return e.tag===5||e.tag===3||e.tag===4}function id(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||th(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function wa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Gi));else if(r!==4&&(e=e.child,e!==null))for(wa(e,t,n),e=e.sibling;e!==null;)wa(e,t,n),e=e.sibling}function xa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(xa(e,t,n),e=e.sibling;e!==null;)xa(e,t,n),e=e.sibling}var Se=null,pt=!1;function Jt(e,t,n){for(n=n.child;n!==null;)nh(e,t,n),n=n.sibling}function nh(e,t,n){if(Rt&&typeof Rt.onCommitFiberUnmount=="function")try{Rt.onCommitFiberUnmount(xs,n)}catch{}switch(n.tag){case 5:Ne||gr(n,t);case 6:var r=Se,o=pt;Se=null,Jt(e,t,n),Se=r,pt=o,Se!==null&&(pt?(e=Se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Se.removeChild(n.stateNode));break;case 18:Se!==null&&(pt?(e=Se,n=n.stateNode,e.nodeType===8?gl(e.parentNode,n):e.nodeType===1&&gl(e,n),No(e)):gl(Se,n.stateNode));break;case 4:r=Se,o=pt,Se=n.stateNode.containerInfo,pt=!0,Jt(e,t,n),Se=r,pt=o;break;case 0:case 11:case 14:case 15:if(!Ne&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&va(n,t,s),o=o.next}while(o!==r)}Jt(e,t,n);break;case 1:if(!Ne&&(gr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){fe(n,t,l)}Jt(e,t,n);break;case 21:Jt(e,t,n);break;case 22:n.mode&1?(Ne=(r=Ne)||n.memoizedState!==null,Jt(e,t,n),Ne=r):Jt(e,t,n);break;default:Jt(e,t,n)}}function sd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new f0),t.forEach(function(r){var o=E0.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function ut(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:Se=l.stateNode,pt=!1;break e;case 3:Se=l.stateNode.containerInfo,pt=!0;break e;case 4:Se=l.stateNode.containerInfo,pt=!0;break e}l=l.return}if(Se===null)throw Error(R(160));nh(i,s,o),Se=null,pt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){fe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)rh(t,e),t=t.sibling}function rh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ut(t,e),Ct(e),r&4){try{wo(3,e,e.return),Ns(3,e)}catch(v){fe(e,e.return,v)}try{wo(5,e,e.return)}catch(v){fe(e,e.return,v)}}break;case 1:ut(t,e),Ct(e),r&512&&n!==null&&gr(n,n.return);break;case 5:if(ut(t,e),Ct(e),r&512&&n!==null&&gr(n,n.return),e.flags&32){var o=e.stateNode;try{ko(o,"")}catch(v){fe(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&Pf(o,i),Hl(l,s);var u=Hl(l,i);for(s=0;s<a.length;s+=2){var f=a[s],d=a[s+1];f==="style"?_f(o,d):f==="dangerouslySetInnerHTML"?Nf(o,d):f==="children"?ko(o,d):Ga(o,f,d,u)}switch(l){case"input":Fl(o,i);break;case"textarea":bf(o,i);break;case"select":var c=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?yr(o,!!i.multiple,m,!1):c!==!!i.multiple&&(i.defaultValue!=null?yr(o,!!i.multiple,i.defaultValue,!0):yr(o,!!i.multiple,i.multiple?[]:"",!1))}o[jo]=i}catch(v){fe(e,e.return,v)}}break;case 6:if(ut(t,e),Ct(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){fe(e,e.return,v)}}break;case 3:if(ut(t,e),Ct(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{No(t.containerInfo)}catch(v){fe(e,e.return,v)}break;case 4:ut(t,e),Ct(e);break;case 13:ut(t,e),Ct(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Nu=pe())),r&4&&sd(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(Ne=(u=Ne)||f,ut(t,e),Ne=u):ut(t,e),Ct(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(I=e,f=e.child;f!==null;){for(d=I=f;I!==null;){switch(c=I,m=c.child,c.tag){case 0:case 11:case 14:case 15:wo(4,c,c.return);break;case 1:gr(c,c.return);var y=c.stateNode;if(typeof y.componentWillUnmount=="function"){r=c,n=c.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){fe(r,n,v)}}break;case 5:gr(c,c.return);break;case 22:if(c.memoizedState!==null){ad(d);continue}}m!==null?(m.return=c,I=m):ad(d)}f=f.sibling}e:for(f=null,d=e;;){if(d.tag===5){if(f===null){f=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=d.stateNode,a=d.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Rf("display",s))}catch(v){fe(e,e.return,v)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(v){fe(e,e.return,v)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:ut(t,e),Ct(e),r&4&&sd(e);break;case 21:break;default:ut(t,e),Ct(e)}}function Ct(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(th(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(ko(o,""),r.flags&=-33);var i=id(e);xa(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,l=id(e);wa(e,l,s);break;default:throw Error(R(161))}}catch(a){fe(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function h0(e,t,n){I=e,oh(e)}function oh(e,t,n){for(var r=(e.mode&1)!==0;I!==null;){var o=I,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||yi;if(!s){var l=o.alternate,a=l!==null&&l.memoizedState!==null||Ne;l=yi;var u=Ne;if(yi=s,(Ne=a)&&!u)for(I=o;I!==null;)s=I,a=s.child,s.tag===22&&s.memoizedState!==null?ud(o):a!==null?(a.return=s,I=a):ud(o);for(;i!==null;)I=i,oh(i),i=i.sibling;I=o,yi=l,Ne=u}ld(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,I=i):ld(e)}}function ld(e){for(;I!==null;){var t=I;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ne||Ns(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ne)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ct(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Wc(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Wc(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&No(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}Ne||t.flags&512&&ya(t)}catch(c){fe(t,t.return,c)}}if(t===e){I=null;break}if(n=t.sibling,n!==null){n.return=t.return,I=n;break}I=t.return}}function ad(e){for(;I!==null;){var t=I;if(t===e){I=null;break}var n=t.sibling;if(n!==null){n.return=t.return,I=n;break}I=t.return}}function ud(e){for(;I!==null;){var t=I;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ns(4,t)}catch(a){fe(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){fe(t,o,a)}}var i=t.return;try{ya(t)}catch(a){fe(t,i,a)}break;case 5:var s=t.return;try{ya(t)}catch(a){fe(t,s,a)}}}catch(a){fe(t,t.return,a)}if(t===e){I=null;break}var l=t.sibling;if(l!==null){l.return=t.return,I=l;break}I=t.return}}var m0=Math.ceil,ss=Kt.ReactCurrentDispatcher,bu=Kt.ReactCurrentOwner,it=Kt.ReactCurrentBatchConfig,X=0,we=null,he=null,Ee=0,Qe=0,vr=On(0),ve=0,Fo=null,Yn=0,Rs=0,Tu=0,xo=null,Ie=null,Nu=0,Br=1/0,It=null,ls=!1,Sa=null,Sn=null,wi=!1,hn=null,as=0,So=0,Ea=null,Li=-1,Ii=0;function je(){return X&6?pe():Li!==-1?Li:Li=pe()}function En(e){return e.mode&1?X&2&&Ee!==0?Ee&-Ee:Zv.transition!==null?(Ii===0&&(Ii=Bf()),Ii):(e=J,e!==0||(e=window.event,e=e===void 0?16:Gf(e.type)),e):1}function gt(e,t,n,r){if(50<So)throw So=0,Ea=null,Error(R(185));Ko(e,n,r),(!(X&2)||e!==we)&&(e===we&&(!(X&2)&&(Rs|=n),ve===4&&ln(e,Ee)),$e(e,r),n===1&&X===0&&!(t.mode&1)&&(Br=pe()+500,Ps&&Mn()))}function $e(e,t){var n=e.callbackNode;Zg(e,t);var r=Wi(e,e===we?Ee:0);if(r===0)n!==null&&yc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&yc(n),t===1)e.tag===0?qv(cd.bind(null,e)):hp(cd.bind(null,e)),Kv(function(){!(X&6)&&Mn()}),n=null;else{switch(Vf(r)){case 1:n=eu;break;case 4:n=$f;break;case 16:n=Hi;break;case 536870912:n=Uf;break;default:n=Hi}n=fh(n,ih.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ih(e,t){if(Li=-1,Ii=0,X&6)throw Error(R(327));var n=e.callbackNode;if(Cr()&&e.callbackNode!==n)return null;var r=Wi(e,e===we?Ee:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=us(e,r);else{t=r;var o=X;X|=2;var i=lh();(we!==e||Ee!==t)&&(It=null,Br=pe()+500,Vn(e,t));do try{y0();break}catch(l){sh(e,l)}while(!0);pu(),ss.current=i,X=o,he!==null?t=0:(we=null,Ee=0,t=ve)}if(t!==0){if(t===2&&(o=Gl(e),o!==0&&(r=o,t=Ca(e,o))),t===1)throw n=Fo,Vn(e,0),ln(e,r),$e(e,pe()),n;if(t===6)ln(e,r);else{if(o=e.current.alternate,!(r&30)&&!g0(o)&&(t=us(e,r),t===2&&(i=Gl(e),i!==0&&(r=i,t=Ca(e,i))),t===1))throw n=Fo,Vn(e,0),ln(e,r),$e(e,pe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:Ln(e,Ie,It);break;case 3:if(ln(e,r),(r&130023424)===r&&(t=Nu+500-pe(),10<t)){if(Wi(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){je(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ra(Ln.bind(null,e,Ie,It),t);break}Ln(e,Ie,It);break;case 4:if(ln(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-mt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=pe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*m0(r/1960))-r,10<r){e.timeoutHandle=ra(Ln.bind(null,e,Ie,It),r);break}Ln(e,Ie,It);break;case 5:Ln(e,Ie,It);break;default:throw Error(R(329))}}}return $e(e,pe()),e.callbackNode===n?ih.bind(null,e):null}function Ca(e,t){var n=xo;return e.current.memoizedState.isDehydrated&&(Vn(e,t).flags|=256),e=us(e,t),e!==2&&(t=Ie,Ie=n,t!==null&&ka(t)),e}function ka(e){Ie===null?Ie=e:Ie.push.apply(Ie,e)}function g0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!vt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ln(e,t){for(t&=~Tu,t&=~Rs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-mt(t),r=1<<n;e[n]=-1,t&=~r}}function cd(e){if(X&6)throw Error(R(327));Cr();var t=Wi(e,0);if(!(t&1))return $e(e,pe()),null;var n=us(e,t);if(e.tag!==0&&n===2){var r=Gl(e);r!==0&&(t=r,n=Ca(e,r))}if(n===1)throw n=Fo,Vn(e,0),ln(e,t),$e(e,pe()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Ln(e,Ie,It),$e(e,pe()),null}function Ru(e,t){var n=X;X|=1;try{return e(t)}finally{X=n,X===0&&(Br=pe()+500,Ps&&Mn())}}function Gn(e){hn!==null&&hn.tag===0&&!(X&6)&&Cr();var t=X;X|=1;var n=it.transition,r=J;try{if(it.transition=null,J=1,e)return e()}finally{J=r,it.transition=n,X=t,!(X&6)&&Mn()}}function _u(){Qe=vr.current,ie(vr)}function Vn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Qv(n)),he!==null)for(n=he.return;n!==null;){var r=n;switch(cu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Xi();break;case 3:$r(),ie(ze),ie(Re),wu();break;case 5:yu(r);break;case 4:$r();break;case 13:ie(ae);break;case 19:ie(ae);break;case 10:hu(r.type._context);break;case 22:case 23:_u()}n=n.return}if(we=e,he=e=Cn(e.current,null),Ee=Qe=t,ve=0,Fo=null,Tu=Rs=Yn=0,Ie=xo=null,Dn!==null){for(t=0;t<Dn.length;t++)if(n=Dn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Dn=null}return e}function sh(e,t){do{var n=he;try{if(pu(),Mi.current=is,os){for(var r=ue.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}os=!1}if(Kn=0,ye=me=ue=null,yo=!1,Io=0,bu.current=null,n===null||n.return===null){ve=1,Fo=t,he=null;break}e:{var i=e,s=n.return,l=n,a=t;if(t=Ee,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,f=l,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var c=f.alternate;c?(f.updateQueue=c.updateQueue,f.memoizedState=c.memoizedState,f.lanes=c.lanes):(f.updateQueue=null,f.memoizedState=null)}var m=qc(s);if(m!==null){m.flags&=-257,Zc(m,s,l,i,t),m.mode&1&&Xc(i,u,t),t=m,a=u;var y=t.updateQueue;if(y===null){var v=new Set;v.add(a),t.updateQueue=v}else y.add(a);break e}else{if(!(t&1)){Xc(i,u,t),Ou();break e}a=Error(R(426))}}else if(se&&l.mode&1){var x=qc(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),Zc(x,s,l,i,t),du(Ur(a,l));break e}}i=a=Ur(a,l),ve!==4&&(ve=2),xo===null?xo=[i]:xo.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=Vp(i,a,t);Hc(i,h);break e;case 1:l=a;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Sn===null||!Sn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var E=Hp(i,l,t);Hc(i,E);break e}}i=i.return}while(i!==null)}uh(n)}catch(C){t=C,he===n&&n!==null&&(he=n=n.return);continue}break}while(!0)}function lh(){var e=ss.current;return ss.current=is,e===null?is:e}function Ou(){(ve===0||ve===3||ve===2)&&(ve=4),we===null||!(Yn&268435455)&&!(Rs&268435455)||ln(we,Ee)}function us(e,t){var n=X;X|=2;var r=lh();(we!==e||Ee!==t)&&(It=null,Vn(e,t));do try{v0();break}catch(o){sh(e,o)}while(!0);if(pu(),X=n,ss.current=r,he!==null)throw Error(R(261));return we=null,Ee=0,ve}function v0(){for(;he!==null;)ah(he)}function y0(){for(;he!==null&&!Vg();)ah(he)}function ah(e){var t=dh(e.alternate,e,Qe);e.memoizedProps=e.pendingProps,t===null?uh(e):he=t,bu.current=null}function uh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=d0(n,t),n!==null){n.flags&=32767,he=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ve=6,he=null;return}}else if(n=c0(n,t,Qe),n!==null){he=n;return}if(t=t.sibling,t!==null){he=t;return}he=t=e}while(t!==null);ve===0&&(ve=5)}function Ln(e,t,n){var r=J,o=it.transition;try{it.transition=null,J=1,w0(e,t,n,r)}finally{it.transition=o,J=r}return null}function w0(e,t,n,r){do Cr();while(hn!==null);if(X&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Jg(e,i),e===we&&(he=we=null,Ee=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||wi||(wi=!0,fh(Hi,function(){return Cr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=it.transition,it.transition=null;var s=J;J=1;var l=X;X|=4,bu.current=null,p0(e,n),rh(n,e),Fv(ta),Qi=!!ea,ta=ea=null,e.current=n,h0(n),Hg(),X=l,J=s,it.transition=i}else e.current=n;if(wi&&(wi=!1,hn=e,as=o),i=e.pendingLanes,i===0&&(Sn=null),Kg(n.stateNode),$e(e,pe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(ls)throw ls=!1,e=Sa,Sa=null,e;return as&1&&e.tag!==0&&Cr(),i=e.pendingLanes,i&1?e===Ea?So++:(So=0,Ea=e):So=0,Mn(),null}function Cr(){if(hn!==null){var e=Vf(as),t=it.transition,n=J;try{if(it.transition=null,J=16>e?16:e,hn===null)var r=!1;else{if(e=hn,hn=null,as=0,X&6)throw Error(R(331));var o=X;for(X|=4,I=e.current;I!==null;){var i=I,s=i.child;if(I.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(I=u;I!==null;){var f=I;switch(f.tag){case 0:case 11:case 15:wo(8,f,i)}var d=f.child;if(d!==null)d.return=f,I=d;else for(;I!==null;){f=I;var c=f.sibling,m=f.return;if(eh(f),f===u){I=null;break}if(c!==null){c.return=m,I=c;break}I=m}}}var y=i.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var x=v.sibling;v.sibling=null,v=x}while(v!==null)}}I=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,I=s;else e:for(;I!==null;){if(i=I,i.flags&2048)switch(i.tag){case 0:case 11:case 15:wo(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,I=h;break e}I=i.return}}var p=e.current;for(I=p;I!==null;){s=I;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,I=g;else e:for(s=p;I!==null;){if(l=I,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Ns(9,l)}}catch(C){fe(l,l.return,C)}if(l===s){I=null;break e}var E=l.sibling;if(E!==null){E.return=l.return,I=E;break e}I=l.return}}if(X=o,Mn(),Rt&&typeof Rt.onPostCommitFiberRoot=="function")try{Rt.onPostCommitFiberRoot(xs,e)}catch{}r=!0}return r}finally{J=n,it.transition=t}}return!1}function dd(e,t,n){t=Ur(n,t),t=Vp(e,t,1),e=xn(e,t,1),t=je(),e!==null&&(Ko(e,1,t),$e(e,t))}function fe(e,t,n){if(e.tag===3)dd(e,e,n);else for(;t!==null;){if(t.tag===3){dd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Sn===null||!Sn.has(r))){e=Ur(n,e),e=Hp(t,e,1),t=xn(t,e,1),e=je(),t!==null&&(Ko(t,1,e),$e(t,e));break}}t=t.return}}function x0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=je(),e.pingedLanes|=e.suspendedLanes&n,we===e&&(Ee&n)===n&&(ve===4||ve===3&&(Ee&130023424)===Ee&&500>pe()-Nu?Vn(e,0):Tu|=n),$e(e,t)}function ch(e,t){t===0&&(e.mode&1?(t=ui,ui<<=1,!(ui&130023424)&&(ui=4194304)):t=1);var n=je();e=Vt(e,t),e!==null&&(Ko(e,t,n),$e(e,n))}function S0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ch(e,n)}function E0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),ch(e,n)}var dh;dh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ze.current)De=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return De=!1,u0(e,t,n);De=!!(e.flags&131072)}else De=!1,se&&t.flags&1048576&&mp(t,Ji,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ai(e,t),e=t.pendingProps;var o=Dr(t,Re.current);Er(t,n),o=Su(null,t,r,e,o,n);var i=Eu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Fe(r)?(i=!0,qi(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,gu(t),o.updater=Ts,t.stateNode=o,o._reactInternals=t,ca(t,r,e,n),t=pa(null,t,r,!0,i,n)):(t.tag=0,se&&i&&uu(t),Oe(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ai(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=k0(r),e=ct(r,e),o){case 0:t=fa(null,t,r,e,n);break e;case 1:t=td(null,t,r,e,n);break e;case 11:t=Jc(null,t,r,e,n);break e;case 14:t=ed(null,t,r,ct(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ct(r,o),fa(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ct(r,o),td(e,t,r,o,n);case 3:e:{if(Yp(t),e===null)throw Error(R(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Sp(e,t),ns(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Ur(Error(R(423)),t),t=nd(e,t,r,n,o);break e}else if(r!==o){o=Ur(Error(R(424)),t),t=nd(e,t,r,n,o);break e}else for(Ye=wn(t.stateNode.containerInfo.firstChild),Ge=t,se=!0,ht=null,n=wp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(zr(),r===o){t=Ht(e,t,n);break e}Oe(e,t,r,n)}t=t.child}return t;case 5:return Ep(t),e===null&&la(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,na(r,o)?s=null:i!==null&&na(r,i)&&(t.flags|=32),Kp(e,t),Oe(e,t,s,n),t.child;case 6:return e===null&&la(t),null;case 13:return Gp(e,t,n);case 4:return vu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Fr(t,null,r,n):Oe(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ct(r,o),Jc(e,t,r,o,n);case 7:return Oe(e,t,t.pendingProps,n),t.child;case 8:return Oe(e,t,t.pendingProps.children,n),t.child;case 12:return Oe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,te(es,r._currentValue),r._currentValue=s,i!==null)if(vt(i.value,s)){if(i.children===o.children&&!ze.current){t=Ht(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){s=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=$t(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?a.next=a:(a.next=f.next,f.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),aa(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(R(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),aa(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Oe(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Er(t,n),o=st(o),r=r(o),t.flags|=1,Oe(e,t,r,n),t.child;case 14:return r=t.type,o=ct(r,t.pendingProps),o=ct(r.type,o),ed(e,t,r,o,n);case 15:return Wp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ct(r,o),Ai(e,t),t.tag=1,Fe(r)?(e=!0,qi(t)):e=!1,Er(t,n),Bp(t,r,o),ca(t,r,o,n),pa(null,t,r,!0,e,n);case 19:return Xp(e,t,n);case 22:return Qp(e,t,n)}throw Error(R(156,t.tag))};function fh(e,t){return Ff(e,t)}function C0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ot(e,t,n,r){return new C0(e,t,n,r)}function Mu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function k0(e){if(typeof e=="function")return Mu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===qa)return 11;if(e===Za)return 14}return 2}function Cn(e,t){var n=e.alternate;return n===null?(n=ot(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Di(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Mu(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case lr:return Hn(n.children,o,i,t);case Xa:s=8,o|=8;break;case Al:return e=ot(12,n,t,o|2),e.elementType=Al,e.lanes=i,e;case Ll:return e=ot(13,n,t,o),e.elementType=Ll,e.lanes=i,e;case Il:return e=ot(19,n,t,o),e.elementType=Il,e.lanes=i,e;case Ef:return _s(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case xf:s=10;break e;case Sf:s=9;break e;case qa:s=11;break e;case Za:s=14;break e;case rn:s=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=ot(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Hn(e,t,n,r){return e=ot(7,e,r,t),e.lanes=n,e}function _s(e,t,n,r){return e=ot(22,e,r,t),e.elementType=Ef,e.lanes=n,e.stateNode={isHidden:!1},e}function kl(e,t,n){return e=ot(6,e,null,t),e.lanes=n,e}function Pl(e,t,n){return t=ot(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function P0(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=il(0),this.expirationTimes=il(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=il(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function ju(e,t,n,r,o,i,s,l,a){return e=new P0(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ot(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},gu(i),e}function b0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:sr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function ph(e){if(!e)return Pn;e=e._reactInternals;e:{if(Jn(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Fe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(Fe(n))return pp(e,n,t)}return t}function hh(e,t,n,r,o,i,s,l,a){return e=ju(n,r,!0,e,o,i,s,l,a),e.context=ph(null),n=e.current,r=je(),o=En(n),i=$t(r,o),i.callback=t??null,xn(n,i,o),e.current.lanes=o,Ko(e,o,r),$e(e,r),e}function Os(e,t,n,r){var o=t.current,i=je(),s=En(o);return n=ph(n),t.context===null?t.context=n:t.pendingContext=n,t=$t(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=xn(o,t,s),e!==null&&(gt(e,o,s,i),Oi(e,o,s)),s}function cs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function fd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Au(e,t){fd(e,t),(e=e.alternate)&&fd(e,t)}function T0(){return null}var mh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Lu(e){this._internalRoot=e}Ms.prototype.render=Lu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));Os(e,t,null,null)};Ms.prototype.unmount=Lu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Gn(function(){Os(null,e,null,null)}),t[Bt]=null}};function Ms(e){this._internalRoot=e}Ms.prototype.unstable_scheduleHydration=function(e){if(e){var t=Qf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<sn.length&&t!==0&&t<sn[n].priority;n++);sn.splice(n,0,e),n===0&&Yf(e)}};function Iu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function js(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function pd(){}function N0(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=cs(s);i.call(u)}}var s=hh(t,r,e,0,null,!1,!1,"",pd);return e._reactRootContainer=s,e[Bt]=s.current,Oo(e.nodeType===8?e.parentNode:e),Gn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=cs(a);l.call(u)}}var a=ju(e,0,!1,null,null,!1,!1,"",pd);return e._reactRootContainer=a,e[Bt]=a.current,Oo(e.nodeType===8?e.parentNode:e),Gn(function(){Os(t,a,n,r)}),a}function As(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var l=o;o=function(){var a=cs(s);l.call(a)}}Os(t,s,e,o)}else s=N0(n,t,e,o,r);return cs(s)}Hf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=co(t.pendingLanes);n!==0&&(tu(t,n|1),$e(t,pe()),!(X&6)&&(Br=pe()+500,Mn()))}break;case 13:Gn(function(){var r=Vt(e,1);if(r!==null){var o=je();gt(r,e,1,o)}}),Au(e,1)}};nu=function(e){if(e.tag===13){var t=Vt(e,134217728);if(t!==null){var n=je();gt(t,e,134217728,n)}Au(e,134217728)}};Wf=function(e){if(e.tag===13){var t=En(e),n=Vt(e,t);if(n!==null){var r=je();gt(n,e,t,r)}Au(e,t)}};Qf=function(){return J};Kf=function(e,t){var n=J;try{return J=e,t()}finally{J=n}};Ql=function(e,t,n){switch(t){case"input":if(Fl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ks(r);if(!o)throw Error(R(90));kf(r),Fl(r,o)}}}break;case"textarea":bf(e,n);break;case"select":t=n.value,t!=null&&yr(e,!!n.multiple,t,!1)}};jf=Ru;Af=Gn;var R0={usingClientEntryPoint:!1,Events:[Go,dr,ks,Of,Mf,Ru]},io={findFiberByHostInstance:In,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},_0={bundleType:io.bundleType,version:io.version,rendererPackageName:io.rendererPackageName,rendererConfig:io.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Kt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Df(e),e===null?null:e.stateNode},findFiberByHostInstance:io.findFiberByHostInstance||T0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var xi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!xi.isDisabled&&xi.supportsFiber)try{xs=xi.inject(_0),Rt=xi}catch{}}Ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=R0;Ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Iu(t))throw Error(R(200));return b0(e,t,null,n)};Ze.createRoot=function(e,t){if(!Iu(e))throw Error(R(299));var n=!1,r="",o=mh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=ju(e,1,!1,null,null,n,!1,r,o),e[Bt]=t.current,Oo(e.nodeType===8?e.parentNode:e),new Lu(t)};Ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=Df(t),e=e===null?null:e.stateNode,e};Ze.flushSync=function(e){return Gn(e)};Ze.hydrate=function(e,t,n){if(!js(t))throw Error(R(200));return As(null,e,t,!0,n)};Ze.hydrateRoot=function(e,t,n){if(!Iu(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=mh;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=hh(t,null,e,1,n??null,o,!1,i,s),e[Bt]=t.current,Oo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Ms(t)};Ze.render=function(e,t,n){if(!js(t))throw Error(R(200));return As(null,e,t,!1,n)};Ze.unmountComponentAtNode=function(e){if(!js(e))throw Error(R(40));return e._reactRootContainer?(Gn(function(){As(null,null,e,!1,function(){e._reactRootContainer=null,e[Bt]=null})}),!0):!1};Ze.unstable_batchedUpdates=Ru;Ze.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!js(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return As(e,t,n,!1,r)};Ze.version="18.3.1-next-f1338f8080-20240426";function gh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(gh)}catch(e){console.error(e)}}gh(),gf.exports=Ze;var qo=gf.exports;const vh=rf(qo);var yh,hd=qo;yh=hd.createRoot,hd.hydrateRoot;const O0=1,M0=1e6;let bl=0;function j0(){return bl=(bl+1)%Number.MAX_SAFE_INTEGER,bl.toString()}const Tl=new Map,md=e=>{if(Tl.has(e))return;const t=setTimeout(()=>{Tl.delete(e),Eo({type:"REMOVE_TOAST",toastId:e})},M0);Tl.set(e,t)},A0=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,O0)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?md(n):e.toasts.forEach(r=>{md(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},zi=[];let Fi={toasts:[]};function Eo(e){Fi=A0(Fi,e),zi.forEach(t=>{t(Fi)})}function L0({...e}){const t=j0(),n=o=>Eo({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>Eo({type:"DISMISS_TOAST",toastId:t});return Eo({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function I0(){const[e,t]=w.useState(Fi);return w.useEffect(()=>(zi.push(t),()=>{const n=zi.indexOf(t);n>-1&&zi.splice(n,1)}),[e]),{...e,toast:L0,dismiss:n=>Eo({type:"DISMISS_TOAST",toastId:n})}}function ge(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function D0(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function wh(...e){return t=>e.forEach(n=>D0(n,t))}function yt(...e){return w.useCallback(wh(...e),e)}function z0(e,t=[]){let n=[];function r(i,s){const l=w.createContext(s),a=n.length;n=[...n,s];function u(d){const{scope:c,children:m,...y}=d,v=(c==null?void 0:c[e][a])||l,x=w.useMemo(()=>y,Object.values(y));return S.jsx(v.Provider,{value:x,children:m})}function f(d,c){const m=(c==null?void 0:c[e][a])||l,y=w.useContext(m);if(y)return y;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,f]}const o=()=>{const i=n.map(s=>w.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return w.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,F0(o,...t)]}function F0(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const d=a(i)[`__scope${u}`];return{...l,...d}},{});return w.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var ds=w.forwardRef((e,t)=>{const{children:n,...r}=e,o=w.Children.toArray(n),i=o.find($0);if(i){const s=i.props.children,l=o.map(a=>a===i?w.Children.count(s)>1?w.Children.only(null):w.isValidElement(s)?s.props.children:null:a);return S.jsx(Pa,{...r,ref:t,children:w.isValidElement(s)?w.cloneElement(s,void 0,l):null})}return S.jsx(Pa,{...r,ref:t,children:n})});ds.displayName="Slot";var Pa=w.forwardRef((e,t)=>{const{children:n,...r}=e;if(w.isValidElement(n)){const o=B0(n);return w.cloneElement(n,{...U0(r,n.props),ref:t?wh(t,o):o})}return w.Children.count(n)>1?w.Children.only(null):null});Pa.displayName="SlotClone";var xh=({children:e})=>S.jsx(S.Fragment,{children:e});function $0(e){return w.isValidElement(e)&&e.type===xh}function U0(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{i(...l),o(...l)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function B0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function V0(e){const t=e+"CollectionProvider",[n,r]=z0(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=m=>{const{scope:y,children:v}=m,x=O.useRef(null),h=O.useRef(new Map).current;return S.jsx(o,{scope:y,itemMap:h,collectionRef:x,children:v})};s.displayName=t;const l=e+"CollectionSlot",a=O.forwardRef((m,y)=>{const{scope:v,children:x}=m,h=i(l,v),p=yt(y,h.collectionRef);return S.jsx(ds,{ref:p,children:x})});a.displayName=l;const u=e+"CollectionItemSlot",f="data-radix-collection-item",d=O.forwardRef((m,y)=>{const{scope:v,children:x,...h}=m,p=O.useRef(null),g=yt(y,p),E=i(u,v);return O.useEffect(()=>(E.itemMap.set(p,{ref:p,...h}),()=>void E.itemMap.delete(p))),S.jsx(ds,{[f]:"",ref:g,children:x})});d.displayName=u;function c(m){const y=i(e+"CollectionConsumer",m);return O.useCallback(()=>{const x=y.collectionRef.current;if(!x)return[];const h=Array.from(x.querySelectorAll(`[${f}]`));return Array.from(y.itemMap.values()).sort((E,C)=>h.indexOf(E.ref.current)-h.indexOf(C.ref.current))},[y.collectionRef,y.itemMap])}return[{Provider:s,Slot:a,ItemSlot:d},c,r]}function Sh(e,t=[]){let n=[];function r(i,s){const l=w.createContext(s),a=n.length;n=[...n,s];const u=d=>{var h;const{scope:c,children:m,...y}=d,v=((h=c==null?void 0:c[e])==null?void 0:h[a])||l,x=w.useMemo(()=>y,Object.values(y));return S.jsx(v.Provider,{value:x,children:m})};u.displayName=i+"Provider";function f(d,c){var v;const m=((v=c==null?void 0:c[e])==null?void 0:v[a])||l,y=w.useContext(m);if(y)return y;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[u,f]}const o=()=>{const i=n.map(s=>w.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return w.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,H0(o,...t)]}function H0(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const d=a(i)[`__scope${u}`];return{...l,...d}},{});return w.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var W0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Be=W0.reduce((e,t)=>{const n=w.forwardRef((r,o)=>{const{asChild:i,...s}=r,l=i?ds:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),S.jsx(l,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Eh(e,t){e&&qo.flushSync(()=>e.dispatchEvent(t))}function Ot(e){const t=w.useRef(e);return w.useEffect(()=>{t.current=e}),w.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Q0(e,t=globalThis==null?void 0:globalThis.document){const n=Ot(e);w.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var K0="DismissableLayer",ba="dismissableLayer.update",Y0="dismissableLayer.pointerDownOutside",G0="dismissableLayer.focusOutside",gd,Ch=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Du=w.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:l,...a}=e,u=w.useContext(Ch),[f,d]=w.useState(null),c=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=w.useState({}),y=yt(t,T=>d(T)),v=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),h=v.indexOf(x),p=f?v.indexOf(f):-1,g=u.layersWithOutsidePointerEventsDisabled.size>0,E=p>=h,C=q0(T=>{const P=T.target,j=[...u.branches].some(M=>M.contains(P));!E||j||(o==null||o(T),s==null||s(T),T.defaultPrevented||l==null||l())},c),b=Z0(T=>{const P=T.target;[...u.branches].some(M=>M.contains(P))||(i==null||i(T),s==null||s(T),T.defaultPrevented||l==null||l())},c);return Q0(T=>{p===u.layers.size-1&&(r==null||r(T),!T.defaultPrevented&&l&&(T.preventDefault(),l()))},c),w.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(gd=c.body.style.pointerEvents,c.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),vd(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(c.body.style.pointerEvents=gd)}},[f,c,n,u]),w.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),vd())},[f,u]),w.useEffect(()=>{const T=()=>m({});return document.addEventListener(ba,T),()=>document.removeEventListener(ba,T)},[]),S.jsx(Be.div,{...a,ref:y,style:{pointerEvents:g?E?"auto":"none":void 0,...e.style},onFocusCapture:ge(e.onFocusCapture,b.onFocusCapture),onBlurCapture:ge(e.onBlurCapture,b.onBlurCapture),onPointerDownCapture:ge(e.onPointerDownCapture,C.onPointerDownCapture)})});Du.displayName=K0;var X0="DismissableLayerBranch",kh=w.forwardRef((e,t)=>{const n=w.useContext(Ch),r=w.useRef(null),o=yt(t,r);return w.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),S.jsx(Be.div,{...e,ref:o})});kh.displayName=X0;function q0(e,t=globalThis==null?void 0:globalThis.document){const n=Ot(e),r=w.useRef(!1),o=w.useRef(()=>{});return w.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let a=function(){Ph(Y0,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Z0(e,t=globalThis==null?void 0:globalThis.document){const n=Ot(e),r=w.useRef(!1);return w.useEffect(()=>{const o=i=>{i.target&&!r.current&&Ph(G0,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function vd(){const e=new CustomEvent(ba);document.dispatchEvent(e)}function Ph(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Eh(o,i):o.dispatchEvent(i)}var J0=Du,ey=kh,Xn=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},ty="Portal",bh=w.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[o,i]=w.useState(!1);Xn(()=>i(!0),[]);const s=n||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return s?vh.createPortal(S.jsx(Be.div,{...r,ref:t}),s):null});bh.displayName=ty;function ny(e,t){return w.useReducer((n,r)=>t[n][r]??n,e)}var zu=e=>{const{present:t,children:n}=e,r=ry(t),o=typeof n=="function"?n({present:r.isPresent}):w.Children.only(n),i=yt(r.ref,oy(o));return typeof n=="function"||r.isPresent?w.cloneElement(o,{ref:i}):null};zu.displayName="Presence";function ry(e){const[t,n]=w.useState(),r=w.useRef({}),o=w.useRef(e),i=w.useRef("none"),s=e?"mounted":"unmounted",[l,a]=ny(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const u=Si(r.current);i.current=l==="mounted"?u:"none"},[l]),Xn(()=>{const u=r.current,f=o.current;if(f!==e){const c=i.current,m=Si(u);e?a("MOUNT"):m==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(f&&c!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),Xn(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,d=m=>{const v=Si(r.current).includes(m.animationName);if(m.target===t&&v&&(a("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},c=m=>{m.target===t&&(i.current=Si(r.current))};return t.addEventListener("animationstart",c),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",c),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:w.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Si(e){return(e==null?void 0:e.animationName)||"none"}function oy(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function iy({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=sy({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,l=Ot(n),a=w.useCallback(u=>{if(i){const d=typeof u=="function"?u(e):u;d!==e&&l(d)}else o(u)},[i,e,o,l]);return[s,a]}function sy({defaultProp:e,onChange:t}){const n=w.useState(e),[r]=n,o=w.useRef(r),i=Ot(t);return w.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}var ly="VisuallyHidden",Ls=w.forwardRef((e,t)=>S.jsx(Be.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Ls.displayName=ly;var ay=Ls,Fu="ToastProvider",[$u,uy,cy]=V0("Toast"),[Th,KS]=Sh("Toast",[cy]),[dy,Is]=Th(Fu),Nh=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[l,a]=w.useState(null),[u,f]=w.useState(0),d=w.useRef(!1),c=w.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Fu}\`. Expected non-empty \`string\`.`),S.jsx($u.Provider,{scope:t,children:S.jsx(dy,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:l,onViewportChange:a,onToastAdd:w.useCallback(()=>f(m=>m+1),[]),onToastRemove:w.useCallback(()=>f(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:c,children:s})})};Nh.displayName=Fu;var Rh="ToastViewport",fy=["F8"],Ta="toast.viewportPause",Na="toast.viewportResume",_h=w.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=fy,label:o="Notifications ({hotkey})",...i}=e,s=Is(Rh,n),l=uy(n),a=w.useRef(null),u=w.useRef(null),f=w.useRef(null),d=w.useRef(null),c=yt(t,d,s.onViewportChange),m=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),y=s.toastCount>0;w.useEffect(()=>{const x=h=>{var g;r.length!==0&&r.every(E=>h[E]||h.code===E)&&((g=d.current)==null||g.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),w.useEffect(()=>{const x=a.current,h=d.current;if(y&&x&&h){const p=()=>{if(!s.isClosePausedRef.current){const b=new CustomEvent(Ta);h.dispatchEvent(b),s.isClosePausedRef.current=!0}},g=()=>{if(s.isClosePausedRef.current){const b=new CustomEvent(Na);h.dispatchEvent(b),s.isClosePausedRef.current=!1}},E=b=>{!x.contains(b.relatedTarget)&&g()},C=()=>{x.contains(document.activeElement)||g()};return x.addEventListener("focusin",p),x.addEventListener("focusout",E),x.addEventListener("pointermove",p),x.addEventListener("pointerleave",C),window.addEventListener("blur",p),window.addEventListener("focus",g),()=>{x.removeEventListener("focusin",p),x.removeEventListener("focusout",E),x.removeEventListener("pointermove",p),x.removeEventListener("pointerleave",C),window.removeEventListener("blur",p),window.removeEventListener("focus",g)}}},[y,s.isClosePausedRef]);const v=w.useCallback(({tabbingDirection:x})=>{const p=l().map(g=>{const E=g.ref.current,C=[E,...Py(E)];return x==="forwards"?C:C.reverse()});return(x==="forwards"?p.reverse():p).flat()},[l]);return w.useEffect(()=>{const x=d.current;if(x){const h=p=>{var C,b,T;const g=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!g){const P=document.activeElement,j=p.shiftKey;if(p.target===x&&j){(C=u.current)==null||C.focus();return}const L=v({tabbingDirection:j?"backwards":"forwards"}),$=L.findIndex(A=>A===P);Nl(L.slice($+1))?p.preventDefault():j?(b=u.current)==null||b.focus():(T=f.current)==null||T.focus()}};return x.addEventListener("keydown",h),()=>x.removeEventListener("keydown",h)}},[l,v]),S.jsxs(ey,{ref:a,role:"region","aria-label":o.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:y?void 0:"none"},children:[y&&S.jsx(Ra,{ref:u,onFocusFromOutsideViewport:()=>{const x=v({tabbingDirection:"forwards"});Nl(x)}}),S.jsx($u.Slot,{scope:n,children:S.jsx(Be.ol,{tabIndex:-1,...i,ref:c})}),y&&S.jsx(Ra,{ref:f,onFocusFromOutsideViewport:()=>{const x=v({tabbingDirection:"backwards"});Nl(x)}})]})});_h.displayName=Rh;var Oh="ToastFocusProxy",Ra=w.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=Is(Oh,n);return S.jsx(Ls,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const l=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(l))&&r()}})});Ra.displayName=Oh;var Ds="Toast",py="toast.swipeStart",hy="toast.swipeMove",my="toast.swipeCancel",gy="toast.swipeEnd",Mh=w.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[l=!0,a]=iy({prop:r,defaultProp:o,onChange:i});return S.jsx(zu,{present:n||l,children:S.jsx(wy,{open:l,...s,ref:t,onClose:()=>a(!1),onPause:Ot(e.onPause),onResume:Ot(e.onResume),onSwipeStart:ge(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ge(e.onSwipeMove,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:ge(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ge(e.onSwipeEnd,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),a(!1)})})})});Mh.displayName=Ds;var[vy,yy]=Th(Ds,{onClose(){}}),wy=w.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:l,onPause:a,onResume:u,onSwipeStart:f,onSwipeMove:d,onSwipeCancel:c,onSwipeEnd:m,...y}=e,v=Is(Ds,n),[x,h]=w.useState(null),p=yt(t,A=>h(A)),g=w.useRef(null),E=w.useRef(null),C=o||v.duration,b=w.useRef(0),T=w.useRef(C),P=w.useRef(0),{onToastAdd:j,onToastRemove:M}=v,F=Ot(()=>{var Q;(x==null?void 0:x.contains(document.activeElement))&&((Q=v.viewport)==null||Q.focus()),s()}),L=w.useCallback(A=>{!A||A===1/0||(window.clearTimeout(P.current),b.current=new Date().getTime(),P.current=window.setTimeout(F,A))},[F]);w.useEffect(()=>{const A=v.viewport;if(A){const Q=()=>{L(T.current),u==null||u()},B=()=>{const K=new Date().getTime()-b.current;T.current=T.current-K,window.clearTimeout(P.current),a==null||a()};return A.addEventListener(Ta,B),A.addEventListener(Na,Q),()=>{A.removeEventListener(Ta,B),A.removeEventListener(Na,Q)}}},[v.viewport,C,a,u,L]),w.useEffect(()=>{i&&!v.isClosePausedRef.current&&L(C)},[i,C,v.isClosePausedRef,L]),w.useEffect(()=>(j(),()=>M()),[j,M]);const $=w.useMemo(()=>x?Fh(x):null,[x]);return v.viewport?S.jsxs(S.Fragment,{children:[$&&S.jsx(xy,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:$}),S.jsx(vy,{scope:n,onClose:F,children:qo.createPortal(S.jsx($u.ItemSlot,{scope:n,children:S.jsx(J0,{asChild:!0,onEscapeKeyDown:ge(l,()=>{v.isFocusedToastEscapeKeyDownRef.current||F(),v.isFocusedToastEscapeKeyDownRef.current=!1}),children:S.jsx(Be.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":v.swipeDirection,...y,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ge(e.onKeyDown,A=>{A.key==="Escape"&&(l==null||l(A.nativeEvent),A.nativeEvent.defaultPrevented||(v.isFocusedToastEscapeKeyDownRef.current=!0,F()))}),onPointerDown:ge(e.onPointerDown,A=>{A.button===0&&(g.current={x:A.clientX,y:A.clientY})}),onPointerMove:ge(e.onPointerMove,A=>{if(!g.current)return;const Q=A.clientX-g.current.x,B=A.clientY-g.current.y,K=!!E.current,k=["left","right"].includes(v.swipeDirection),_=["left","up"].includes(v.swipeDirection)?Math.min:Math.max,z=k?_(0,Q):0,D=k?0:_(0,B),U=A.pointerType==="touch"?10:2,G={x:z,y:D},le={originalEvent:A,delta:G};K?(E.current=G,Ei(hy,d,le,{discrete:!1})):yd(G,v.swipeDirection,U)?(E.current=G,Ei(py,f,le,{discrete:!1}),A.target.setPointerCapture(A.pointerId)):(Math.abs(Q)>U||Math.abs(B)>U)&&(g.current=null)}),onPointerUp:ge(e.onPointerUp,A=>{const Q=E.current,B=A.target;if(B.hasPointerCapture(A.pointerId)&&B.releasePointerCapture(A.pointerId),E.current=null,g.current=null,Q){const K=A.currentTarget,k={originalEvent:A,delta:Q};yd(Q,v.swipeDirection,v.swipeThreshold)?Ei(gy,m,k,{discrete:!0}):Ei(my,c,k,{discrete:!0}),K.addEventListener("click",_=>_.preventDefault(),{once:!0})}})})})}),v.viewport)})]}):null}),xy=e=>{const{__scopeToast:t,children:n,...r}=e,o=Is(Ds,t),[i,s]=w.useState(!1),[l,a]=w.useState(!1);return Cy(()=>s(!0)),w.useEffect(()=>{const u=window.setTimeout(()=>a(!0),1e3);return()=>window.clearTimeout(u)},[]),l?null:S.jsx(bh,{asChild:!0,children:S.jsx(Ls,{...r,children:i&&S.jsxs(S.Fragment,{children:[o.label," ",n]})})})},Sy="ToastTitle",jh=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return S.jsx(Be.div,{...r,ref:t})});jh.displayName=Sy;var Ey="ToastDescription",Ah=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return S.jsx(Be.div,{...r,ref:t})});Ah.displayName=Ey;var Lh="ToastAction",Ih=w.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?S.jsx(zh,{altText:n,asChild:!0,children:S.jsx(Uu,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Lh}\`. Expected non-empty \`string\`.`),null)});Ih.displayName=Lh;var Dh="ToastClose",Uu=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=yy(Dh,n);return S.jsx(zh,{asChild:!0,children:S.jsx(Be.button,{type:"button",...r,ref:t,onClick:ge(e.onClick,o.onClose)})})});Uu.displayName=Dh;var zh=w.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return S.jsx(Be.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Fh(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),ky(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Fh(r))}}),t}function Ei(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Eh(o,i):o.dispatchEvent(i)}var yd=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function Cy(e=()=>{}){const t=Ot(e);Xn(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function ky(e){return e.nodeType===e.ELEMENT_NODE}function Py(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Nl(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var by=Nh,$h=_h,Uh=Mh,Bh=jh,Vh=Ah,Hh=Ih,Wh=Uu;function Qh(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Qh(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Kh(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Qh(e))&&(r&&(r+=" "),r+=t);return r}const wd=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,xd=Kh,Ty=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return xd(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const f=n==null?void 0:n[u],d=i==null?void 0:i[u];if(f===null)return null;const c=wd(f)||wd(d);return o[u][c]}),l=n&&Object.entries(n).reduce((u,f)=>{let[d,c]=f;return c===void 0||(u[d]=c),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,f)=>{let{class:d,className:c,...m}=f;return Object.entries(m).every(y=>{let[v,x]=y;return Array.isArray(x)?x.includes({...i,...l}[v]):{...i,...l}[v]===x})?[...u,d,c]:u},[]);return xd(e,s,a,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ny=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Yh=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ry={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _y=w.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:s,...l},a)=>w.createElement("svg",{ref:a,...Ry,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Yh("lucide",o),...l},[...s.map(([u,f])=>w.createElement(u,f)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const et=(e,t)=>{const n=w.forwardRef(({className:r,...o},i)=>w.createElement(_y,{ref:i,iconNode:t,className:Yh(`lucide-${Ny(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oy=et("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const My=et("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jy=et("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ay=et("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ly=et("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iy=et("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gh=et("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dy=et("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zy=et("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fy=et("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $y=et("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _a=et("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uy=et("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Bu="-",By=e=>{const t=Hy(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const l=s.split(Bu);return l[0]===""&&l.length!==1&&l.shift(),Xh(l,t)||Vy(s)},getConflictingClassGroupIds:(s,l)=>{const a=n[s]||[];return l&&r[s]?[...a,...r[s]]:a}}},Xh=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Xh(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Bu);return(s=t.validators.find(({validator:l})=>l(i)))==null?void 0:s.classGroupId},Sd=/^\[(.+)\]$/,Vy=e=>{if(Sd.test(e)){const t=Sd.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Hy=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Qy(Object.entries(e.classGroups),n).forEach(([i,s])=>{Oa(s,r,i,t)}),r},Oa=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:Ed(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(Wy(o)){Oa(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{Oa(s,Ed(t,i),n,r)})})},Ed=(e,t)=>{let n=e;return t.split(Bu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Wy=e=>e.isThemeGetter,Qy=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,l])=>[t+s,l])):i);return[n,o]}):e,Ky=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,s)=>{n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}},qh="!",Yy=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,s=l=>{const a=[];let u=0,f=0,d;for(let x=0;x<l.length;x++){let h=l[x];if(u===0){if(h===o&&(r||l.slice(x,x+i)===t)){a.push(l.slice(f,x)),f=x+i;continue}if(h==="/"){d=x;continue}}h==="["?u++:h==="]"&&u--}const c=a.length===0?l:l.substring(f),m=c.startsWith(qh),y=m?c.substring(1):c,v=d&&d>f?d-f:void 0;return{modifiers:a,hasImportantModifier:m,baseClassName:y,maybePostfixModifierPosition:v}};return n?l=>n({className:l,parseClassName:s}):s},Gy=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Xy=e=>({cache:Ky(e.cacheSize),parseClassName:Yy(e),...By(e)}),qy=/\s+/,Zy=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],s=e.trim().split(qy);let l="";for(let a=s.length-1;a>=0;a-=1){const u=s[a],{modifiers:f,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:m}=n(u);let y=!!m,v=r(y?c.substring(0,m):c);if(!v){if(!y){l=u+(l.length>0?" "+l:l);continue}if(v=r(c),!v){l=u+(l.length>0?" "+l:l);continue}y=!1}const x=Gy(f).join(":"),h=d?x+qh:x,p=h+v;if(i.includes(p))continue;i.push(p);const g=o(v,y);for(let E=0;E<g.length;++E){const C=g[E];i.push(h+C)}l=u+(l.length>0?" "+l:l)}return l};function Jy(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Zh(t))&&(r&&(r+=" "),r+=n);return r}const Zh=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Zh(e[r]))&&(n&&(n+=" "),n+=t);return n};function ew(e,...t){let n,r,o,i=s;function s(a){const u=t.reduce((f,d)=>d(f),e());return n=Xy(u),r=n.cache.get,o=n.cache.set,i=l,l(a)}function l(a){const u=r(a);if(u)return u;const f=Zy(a,n);return o(a,f),f}return function(){return i(Jy.apply(null,arguments))}}const re=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Jh=/^\[(?:([a-z-]+):)?(.+)\]$/i,tw=/^\d+\/\d+$/,nw=new Set(["px","full","screen"]),rw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ow=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,iw=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,sw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,lw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,At=e=>kr(e)||nw.has(e)||tw.test(e),en=e=>Yr(e,"length",mw),kr=e=>!!e&&!Number.isNaN(Number(e)),Rl=e=>Yr(e,"number",kr),so=e=>!!e&&Number.isInteger(Number(e)),aw=e=>e.endsWith("%")&&kr(e.slice(0,-1)),H=e=>Jh.test(e),tn=e=>rw.test(e),uw=new Set(["length","size","percentage"]),cw=e=>Yr(e,uw,em),dw=e=>Yr(e,"position",em),fw=new Set(["image","url"]),pw=e=>Yr(e,fw,vw),hw=e=>Yr(e,"",gw),lo=()=>!0,Yr=(e,t,n)=>{const r=Jh.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},mw=e=>ow.test(e)&&!iw.test(e),em=()=>!1,gw=e=>sw.test(e),vw=e=>lw.test(e),yw=()=>{const e=re("colors"),t=re("spacing"),n=re("blur"),r=re("brightness"),o=re("borderColor"),i=re("borderRadius"),s=re("borderSpacing"),l=re("borderWidth"),a=re("contrast"),u=re("grayscale"),f=re("hueRotate"),d=re("invert"),c=re("gap"),m=re("gradientColorStops"),y=re("gradientColorStopPositions"),v=re("inset"),x=re("margin"),h=re("opacity"),p=re("padding"),g=re("saturate"),E=re("scale"),C=re("sepia"),b=re("skew"),T=re("space"),P=re("translate"),j=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto",H,t],L=()=>[H,t],$=()=>["",At,en],A=()=>["auto",kr,H],Q=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],B=()=>["solid","dashed","dotted","double","none"],K=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],k=()=>["start","end","center","between","around","evenly","stretch"],_=()=>["","0",H],z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],D=()=>[kr,H];return{cacheSize:500,separator:":",theme:{colors:[lo],spacing:[At,en],blur:["none","",tn,H],brightness:D(),borderColor:[e],borderRadius:["none","","full",tn,H],borderSpacing:L(),borderWidth:$(),contrast:D(),grayscale:_(),hueRotate:D(),invert:_(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[aw,en],inset:F(),margin:F(),opacity:D(),padding:L(),saturate:D(),scale:D(),sepia:_(),skew:D(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",H]}],container:["container"],columns:[{columns:[tn]}],"break-after":[{"break-after":z()}],"break-before":[{"break-before":z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Q(),H]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",so,H]}],basis:[{basis:F()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",H]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",so,H]}],"grid-cols":[{"grid-cols":[lo]}],"col-start-end":[{col:["auto",{span:["full",so,H]},H]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[lo]}],"row-start-end":[{row:["auto",{span:[so,H]},H]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",H]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",H]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",...k()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...k(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...k(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[T]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[T]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",H,t]}],"min-w":[{"min-w":[H,t,"min","max","fit"]}],"max-w":[{"max-w":[H,t,"none","full","min","max","fit","prose",{screen:[tn]},tn]}],h:[{h:[H,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[H,t,"auto","min","max","fit"]}],"font-size":[{text:["base",tn,en]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Rl]}],"font-family":[{font:[lo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",H]}],"line-clamp":[{"line-clamp":["none",kr,Rl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",At,H]}],"list-image":[{"list-image":["none",H]}],"list-style-type":[{list:["none","disc","decimal",H]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",At,en]}],"underline-offset":[{"underline-offset":["auto",At,H]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Q(),dw]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",cw]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},pw]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[y]}],"gradient-via-pos":[{via:[y]}],"gradient-to-pos":[{to:[y]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...B(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:B()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...B()]}],"outline-offset":[{"outline-offset":[At,H]}],"outline-w":[{outline:[At,en]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:$()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[At,en]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",tn,hw]}],"shadow-color":[{shadow:[lo]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...K(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":K()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",tn,H]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[d]}],saturate:[{saturate:[g]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",H]}],duration:[{duration:D()}],ease:[{ease:["linear","in","out","in-out",H]}],delay:[{delay:D()}],animate:[{animate:["none","spin","ping","pulse","bounce",H]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[E]}],"scale-x":[{"scale-x":[E]}],"scale-y":[{"scale-y":[E]}],rotate:[{rotate:[so,H]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[b]}],"skew-y":[{"skew-y":[b]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",H]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[At,en,Rl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},ww=ew(yw);function er(...e){return ww(Kh(e))}const xw=by,tm=w.forwardRef(({className:e,...t},n)=>S.jsx($h,{ref:n,className:er("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));tm.displayName=$h.displayName;const Sw=Ty("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),nm=w.forwardRef(({className:e,variant:t,...n},r)=>S.jsx(Uh,{ref:r,className:er(Sw({variant:t}),e),...n}));nm.displayName=Uh.displayName;const Ew=w.forwardRef(({className:e,...t},n)=>S.jsx(Hh,{ref:n,className:er("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));Ew.displayName=Hh.displayName;const rm=w.forwardRef(({className:e,...t},n)=>S.jsx(Wh,{ref:n,className:er("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:S.jsx(Uy,{className:"h-4 w-4"})}));rm.displayName=Wh.displayName;const om=w.forwardRef(({className:e,...t},n)=>S.jsx(Bh,{ref:n,className:er("text-sm font-semibold",e),...t}));om.displayName=Bh.displayName;const im=w.forwardRef(({className:e,...t},n)=>S.jsx(Vh,{ref:n,className:er("text-sm opacity-90",e),...t}));im.displayName=Vh.displayName;function Cw(){const{toasts:e}=I0();return S.jsxs(xw,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return S.jsxs(nm,{...i,children:[S.jsxs("div",{className:"grid gap-1",children:[n&&S.jsx(om,{children:n}),r&&S.jsx(im,{children:r})]}),o,S.jsx(rm,{})]},t)}),S.jsx(tm,{})]})}var Cd=["light","dark"],kw="(prefers-color-scheme: dark)",Pw=w.createContext(void 0),bw={setTheme:e=>{},themes:[]},Tw=()=>{var e;return(e=w.useContext(Pw))!=null?e:bw};w.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:i,value:s,attrs:l,nonce:a})=>{let u=i==="system",f=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${l.map(y=>`'${y}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,d=o?Cd.includes(i)&&i?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${i}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",c=(y,v=!1,x=!0)=>{let h=s?s[y]:y,p=v?y+"|| ''":`'${h}'`,g="";return o&&x&&!v&&Cd.includes(y)&&(g+=`d.style.colorScheme = '${y}';`),n==="class"?v||h?g+=`c.add(${p})`:g+="null":h&&(g+=`d[s](n,${p})`),g},m=e?`!function(){${f}${c(e)}}()`:r?`!function(){try{${f}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${kw}',m=window.matchMedia(t);if(m.media!==t||m.matches){${c("dark")}}else{${c("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${c(s?"x[e]":"e",!0)}}${u?"":"else{"+c(i,!1,!1)+"}"}${d}}catch(e){}}()`:`!function(){try{${f}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${c(s?"x[e]":"e",!0)}}else{${c(i,!1,!1)};}${d}}catch(t){}}();`;return w.createElement("script",{nonce:a,dangerouslySetInnerHTML:{__html:m}})});var Nw=e=>{switch(e){case"success":return Ow;case"info":return jw;case"warning":return Mw;case"error":return Aw;default:return null}},Rw=Array(12).fill(0),_w=({visible:e})=>O.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},O.createElement("div",{className:"sonner-spinner"},Rw.map((t,n)=>O.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),Ow=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Mw=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),jw=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),Aw=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),Lw=()=>{let[e,t]=O.useState(document.hidden);return O.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},Ma=1,Iw=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:Ma++,i=this.toasts.find(l=>l.id===o),s=e.dismissible===void 0?!0:e.dismissible;return i?this.toasts=this.toasts.map(l=>l.id===o?(this.publish({...l,...e,id:o,title:n}),{...l,...e,id:o,dismissible:s,title:n}):l):this.addToast({title:n,...r,dismissible:s,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async i=>{if(zw(i)&&!i.ok){o=!1;let s=typeof t.error=="function"?await t.error(`HTTP error! status: ${i.status}`):t.error,l=typeof t.description=="function"?await t.description(`HTTP error! status: ${i.status}`):t.description;this.create({id:n,type:"error",message:s,description:l})}else if(t.success!==void 0){o=!1;let s=typeof t.success=="function"?await t.success(i):t.success,l=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"success",message:s,description:l})}}).catch(async i=>{if(t.error!==void 0){o=!1;let s=typeof t.error=="function"?await t.error(i):t.error,l=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"error",message:s,description:l})}}).finally(()=>{var i;o&&(this.dismiss(n),n=void 0),(i=t.finally)==null||i.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||Ma++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},We=new Iw,Dw=(e,t)=>{let n=(t==null?void 0:t.id)||Ma++;return We.addToast({title:e,...t,id:n}),n},zw=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Fw=Dw,$w=()=>We.toasts;Object.assign(Fw,{success:We.success,info:We.info,warning:We.warning,error:We.error,custom:We.custom,message:We.message,promise:We.promise,dismiss:We.dismiss,loading:We.loading},{getHistory:$w});function Uw(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}Uw(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Ci(e){return e.label!==void 0}var Bw=3,Vw="32px",Hw=4e3,Ww=356,Qw=14,Kw=20,Yw=200;function Gw(...e){return e.filter(Boolean).join(" ")}var Xw=e=>{var t,n,r,o,i,s,l,a,u,f;let{invert:d,toast:c,unstyled:m,interacting:y,setHeights:v,visibleToasts:x,heights:h,index:p,toasts:g,expanded:E,removeToast:C,defaultRichColors:b,closeButton:T,style:P,cancelButtonStyle:j,actionButtonStyle:M,className:F="",descriptionClassName:L="",duration:$,position:A,gap:Q,loadingIcon:B,expandByDefault:K,classNames:k,icons:_,closeButtonAriaLabel:z="Close toast",pauseWhenPageIsHidden:D,cn:U}=e,[G,le]=O.useState(!1),[Ve,Z]=O.useState(!1),[at,Yt]=O.useState(!1),[Gt,Xt]=O.useState(!1),[Jo,tr]=O.useState(0),[jn,qr]=O.useState(0),ei=O.useRef(null),qt=O.useRef(null),Ys=p===0,Gs=p+1<=x,xe=c.type,nr=c.dismissible!==!1,Gm=c.className||"",Xm=c.descriptionClassName||"",ti=O.useMemo(()=>h.findIndex(V=>V.toastId===c.id)||0,[h,c.id]),qm=O.useMemo(()=>{var V;return(V=c.closeButton)!=null?V:T},[c.closeButton,T]),ec=O.useMemo(()=>c.duration||$||Hw,[c.duration,$]),Xs=O.useRef(0),rr=O.useRef(0),tc=O.useRef(0),or=O.useRef(null),[nc,Zm]=A.split("-"),rc=O.useMemo(()=>h.reduce((V,ne,ee)=>ee>=ti?V:V+ne.height,0),[h,ti]),oc=Lw(),Jm=c.invert||d,qs=xe==="loading";rr.current=O.useMemo(()=>ti*Q+rc,[ti,rc]),O.useEffect(()=>{le(!0)},[]),O.useLayoutEffect(()=>{if(!G)return;let V=qt.current,ne=V.style.height;V.style.height="auto";let ee=V.getBoundingClientRect().height;V.style.height=ne,qr(ee),v(St=>St.find(Et=>Et.toastId===c.id)?St.map(Et=>Et.toastId===c.id?{...Et,height:ee}:Et):[{toastId:c.id,height:ee,position:c.position},...St])},[G,c.title,c.description,v,c.id]);let Zt=O.useCallback(()=>{Z(!0),tr(rr.current),v(V=>V.filter(ne=>ne.toastId!==c.id)),setTimeout(()=>{C(c)},Yw)},[c,C,v,rr]);O.useEffect(()=>{if(c.promise&&xe==="loading"||c.duration===1/0||c.type==="loading")return;let V,ne=ec;return E||y||D&&oc?(()=>{if(tc.current<Xs.current){let ee=new Date().getTime()-Xs.current;ne=ne-ee}tc.current=new Date().getTime()})():ne!==1/0&&(Xs.current=new Date().getTime(),V=setTimeout(()=>{var ee;(ee=c.onAutoClose)==null||ee.call(c,c),Zt()},ne)),()=>clearTimeout(V)},[E,y,K,c,ec,Zt,c.promise,xe,D,oc]),O.useEffect(()=>{let V=qt.current;if(V){let ne=V.getBoundingClientRect().height;return qr(ne),v(ee=>[{toastId:c.id,height:ne,position:c.position},...ee]),()=>v(ee=>ee.filter(St=>St.toastId!==c.id))}},[v,c.id]),O.useEffect(()=>{c.delete&&Zt()},[Zt,c.delete]);function eg(){return _!=null&&_.loading?O.createElement("div",{className:"sonner-loader","data-visible":xe==="loading"},_.loading):B?O.createElement("div",{className:"sonner-loader","data-visible":xe==="loading"},B):O.createElement(_w,{visible:xe==="loading"})}return O.createElement("li",{"aria-live":c.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:qt,className:U(F,Gm,k==null?void 0:k.toast,(t=c==null?void 0:c.classNames)==null?void 0:t.toast,k==null?void 0:k.default,k==null?void 0:k[xe],(n=c==null?void 0:c.classNames)==null?void 0:n[xe]),"data-sonner-toast":"","data-rich-colors":(r=c.richColors)!=null?r:b,"data-styled":!(c.jsx||c.unstyled||m),"data-mounted":G,"data-promise":!!c.promise,"data-removed":Ve,"data-visible":Gs,"data-y-position":nc,"data-x-position":Zm,"data-index":p,"data-front":Ys,"data-swiping":at,"data-dismissible":nr,"data-type":xe,"data-invert":Jm,"data-swipe-out":Gt,"data-expanded":!!(E||K&&G),style:{"--index":p,"--toasts-before":p,"--z-index":g.length-p,"--offset":`${Ve?Jo:rr.current}px`,"--initial-height":K?"auto":`${jn}px`,...P,...c.style},onPointerDown:V=>{qs||!nr||(ei.current=new Date,tr(rr.current),V.target.setPointerCapture(V.pointerId),V.target.tagName!=="BUTTON"&&(Yt(!0),or.current={x:V.clientX,y:V.clientY}))},onPointerUp:()=>{var V,ne,ee,St;if(Gt||!nr)return;or.current=null;let Et=Number(((V=qt.current)==null?void 0:V.style.getPropertyValue("--swipe-amount").replace("px",""))||0),ni=new Date().getTime()-((ne=ei.current)==null?void 0:ne.getTime()),tg=Math.abs(Et)/ni;if(Math.abs(Et)>=Kw||tg>.11){tr(rr.current),(ee=c.onDismiss)==null||ee.call(c,c),Zt(),Xt(!0);return}(St=qt.current)==null||St.style.setProperty("--swipe-amount","0px"),Yt(!1)},onPointerMove:V=>{var ne;if(!or.current||!nr)return;let ee=V.clientY-or.current.y,St=V.clientX-or.current.x,Et=(nc==="top"?Math.min:Math.max)(0,ee),ni=V.pointerType==="touch"?10:2;Math.abs(Et)>ni?(ne=qt.current)==null||ne.style.setProperty("--swipe-amount",`${ee}px`):Math.abs(St)>ni&&(or.current=null)}},qm&&!c.jsx?O.createElement("button",{"aria-label":z,"data-disabled":qs,"data-close-button":!0,onClick:qs||!nr?()=>{}:()=>{var V;Zt(),(V=c.onDismiss)==null||V.call(c,c)},className:U(k==null?void 0:k.closeButton,(o=c==null?void 0:c.classNames)==null?void 0:o.closeButton)},O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},O.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),O.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,c.jsx||O.isValidElement(c.title)?c.jsx||c.title:O.createElement(O.Fragment,null,xe||c.icon||c.promise?O.createElement("div",{"data-icon":"",className:U(k==null?void 0:k.icon,(i=c==null?void 0:c.classNames)==null?void 0:i.icon)},c.promise||c.type==="loading"&&!c.icon?c.icon||eg():null,c.type!=="loading"?c.icon||(_==null?void 0:_[xe])||Nw(xe):null):null,O.createElement("div",{"data-content":"",className:U(k==null?void 0:k.content,(s=c==null?void 0:c.classNames)==null?void 0:s.content)},O.createElement("div",{"data-title":"",className:U(k==null?void 0:k.title,(l=c==null?void 0:c.classNames)==null?void 0:l.title)},c.title),c.description?O.createElement("div",{"data-description":"",className:U(L,Xm,k==null?void 0:k.description,(a=c==null?void 0:c.classNames)==null?void 0:a.description)},c.description):null),O.isValidElement(c.cancel)?c.cancel:c.cancel&&Ci(c.cancel)?O.createElement("button",{"data-button":!0,"data-cancel":!0,style:c.cancelButtonStyle||j,onClick:V=>{var ne,ee;Ci(c.cancel)&&nr&&((ee=(ne=c.cancel).onClick)==null||ee.call(ne,V),Zt())},className:U(k==null?void 0:k.cancelButton,(u=c==null?void 0:c.classNames)==null?void 0:u.cancelButton)},c.cancel.label):null,O.isValidElement(c.action)?c.action:c.action&&Ci(c.action)?O.createElement("button",{"data-button":!0,"data-action":!0,style:c.actionButtonStyle||M,onClick:V=>{var ne,ee;Ci(c.action)&&(V.defaultPrevented||((ee=(ne=c.action).onClick)==null||ee.call(ne,V),Zt()))},className:U(k==null?void 0:k.actionButton,(f=c==null?void 0:c.classNames)==null?void 0:f.actionButton)},c.action.label):null))};function kd(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var qw=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:i,className:s,offset:l,theme:a="light",richColors:u,duration:f,style:d,visibleToasts:c=Bw,toastOptions:m,dir:y=kd(),gap:v=Qw,loadingIcon:x,icons:h,containerAriaLabel:p="Notifications",pauseWhenPageIsHidden:g,cn:E=Gw}=e,[C,b]=O.useState([]),T=O.useMemo(()=>Array.from(new Set([n].concat(C.filter(D=>D.position).map(D=>D.position)))),[C,n]),[P,j]=O.useState([]),[M,F]=O.useState(!1),[L,$]=O.useState(!1),[A,Q]=O.useState(a!=="system"?a:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),B=O.useRef(null),K=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),k=O.useRef(null),_=O.useRef(!1),z=O.useCallback(D=>{var U;(U=C.find(G=>G.id===D.id))!=null&&U.delete||We.dismiss(D.id),b(G=>G.filter(({id:le})=>le!==D.id))},[C]);return O.useEffect(()=>We.subscribe(D=>{if(D.dismiss){b(U=>U.map(G=>G.id===D.id?{...G,delete:!0}:G));return}setTimeout(()=>{vh.flushSync(()=>{b(U=>{let G=U.findIndex(le=>le.id===D.id);return G!==-1?[...U.slice(0,G),{...U[G],...D},...U.slice(G+1)]:[D,...U]})})})}),[]),O.useEffect(()=>{if(a!=="system"){Q(a);return}a==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?Q("dark"):Q("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:D})=>{Q(D?"dark":"light")})},[a]),O.useEffect(()=>{C.length<=1&&F(!1)},[C]),O.useEffect(()=>{let D=U=>{var G,le;r.every(Ve=>U[Ve]||U.code===Ve)&&(F(!0),(G=B.current)==null||G.focus()),U.code==="Escape"&&(document.activeElement===B.current||(le=B.current)!=null&&le.contains(document.activeElement))&&F(!1)};return document.addEventListener("keydown",D),()=>document.removeEventListener("keydown",D)},[r]),O.useEffect(()=>{if(B.current)return()=>{k.current&&(k.current.focus({preventScroll:!0}),k.current=null,_.current=!1)}},[B.current]),C.length?O.createElement("section",{"aria-label":`${p} ${K}`,tabIndex:-1},T.map((D,U)=>{var G;let[le,Ve]=D.split("-");return O.createElement("ol",{key:D,dir:y==="auto"?kd():y,tabIndex:-1,ref:B,className:s,"data-sonner-toaster":!0,"data-theme":A,"data-y-position":le,"data-x-position":Ve,style:{"--front-toast-height":`${((G=P[0])==null?void 0:G.height)||0}px`,"--offset":typeof l=="number"?`${l}px`:l||Vw,"--width":`${Ww}px`,"--gap":`${v}px`,...d},onBlur:Z=>{_.current&&!Z.currentTarget.contains(Z.relatedTarget)&&(_.current=!1,k.current&&(k.current.focus({preventScroll:!0}),k.current=null))},onFocus:Z=>{Z.target instanceof HTMLElement&&Z.target.dataset.dismissible==="false"||_.current||(_.current=!0,k.current=Z.relatedTarget)},onMouseEnter:()=>F(!0),onMouseMove:()=>F(!0),onMouseLeave:()=>{L||F(!1)},onPointerDown:Z=>{Z.target instanceof HTMLElement&&Z.target.dataset.dismissible==="false"||$(!0)},onPointerUp:()=>$(!1)},C.filter(Z=>!Z.position&&U===0||Z.position===D).map((Z,at)=>{var Yt,Gt;return O.createElement(Xw,{key:Z.id,icons:h,index:at,toast:Z,defaultRichColors:u,duration:(Yt=m==null?void 0:m.duration)!=null?Yt:f,className:m==null?void 0:m.className,descriptionClassName:m==null?void 0:m.descriptionClassName,invert:t,visibleToasts:c,closeButton:(Gt=m==null?void 0:m.closeButton)!=null?Gt:i,interacting:L,position:D,style:m==null?void 0:m.style,unstyled:m==null?void 0:m.unstyled,classNames:m==null?void 0:m.classNames,cancelButtonStyle:m==null?void 0:m.cancelButtonStyle,actionButtonStyle:m==null?void 0:m.actionButtonStyle,removeToast:z,toasts:C.filter(Xt=>Xt.position==Z.position),heights:P.filter(Xt=>Xt.position==Z.position),setHeights:j,expandByDefault:o,gap:v,loadingIcon:x,expanded:M,pauseWhenPageIsHidden:g,cn:E})}))})):null};const Zw=({...e})=>{const{theme:t="system"}=Tw();return S.jsx(qw,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},Jw=["top","right","bottom","left"],bn=Math.min,Ke=Math.max,fs=Math.round,ki=Math.floor,Tn=e=>({x:e,y:e}),e1={left:"right",right:"left",bottom:"top",top:"bottom"},t1={start:"end",end:"start"};function ja(e,t,n){return Ke(e,bn(t,n))}function Wt(e,t){return typeof e=="function"?e(t):e}function Qt(e){return e.split("-")[0]}function Gr(e){return e.split("-")[1]}function Vu(e){return e==="x"?"y":"x"}function Hu(e){return e==="y"?"height":"width"}function Nn(e){return["top","bottom"].includes(Qt(e))?"y":"x"}function Wu(e){return Vu(Nn(e))}function n1(e,t,n){n===void 0&&(n=!1);const r=Gr(e),o=Wu(e),i=Hu(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=ps(s)),[s,ps(s)]}function r1(e){const t=ps(e);return[Aa(e),t,Aa(t)]}function Aa(e){return e.replace(/start|end/g,t=>t1[t])}function o1(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function i1(e,t,n,r){const o=Gr(e);let i=o1(Qt(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(Aa)))),i}function ps(e){return e.replace(/left|right|bottom|top/g,t=>e1[t])}function s1(e){return{top:0,right:0,bottom:0,left:0,...e}}function sm(e){return typeof e!="number"?s1(e):{top:e,right:e,bottom:e,left:e}}function hs(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Pd(e,t,n){let{reference:r,floating:o}=e;const i=Nn(t),s=Wu(t),l=Hu(s),a=Qt(t),u=i==="y",f=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,c=r[l]/2-o[l]/2;let m;switch(a){case"top":m={x:f,y:r.y-o.height};break;case"bottom":m={x:f,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:d};break;case"left":m={x:r.x-o.width,y:d};break;default:m={x:r.x,y:r.y}}switch(Gr(t)){case"start":m[s]-=c*(n&&u?-1:1);break;case"end":m[s]+=c*(n&&u?-1:1);break}return m}const l1=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,l=i.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:d}=Pd(u,r,a),c=r,m={},y=0;for(let v=0;v<l.length;v++){const{name:x,fn:h}=l[v],{x:p,y:g,data:E,reset:C}=await h({x:f,y:d,initialPlacement:r,placement:c,strategy:o,middlewareData:m,rects:u,platform:s,elements:{reference:e,floating:t}});f=p??f,d=g??d,m={...m,[x]:{...m[x],...E}},C&&y<=50&&(y++,typeof C=="object"&&(C.placement&&(c=C.placement),C.rects&&(u=C.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:f,y:d}=Pd(u,c,a)),v=-1)}return{x:f,y:d,placement:c,strategy:o,middlewareData:m}};async function $o(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:l,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:c=!1,padding:m=0}=Wt(t,e),y=sm(m),x=l[c?d==="floating"?"reference":"floating":d],h=hs(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(x)))==null||n?x:x.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:f,strategy:a})),p=d==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,g=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),E=await(i.isElement==null?void 0:i.isElement(g))?await(i.getScale==null?void 0:i.getScale(g))||{x:1,y:1}:{x:1,y:1},C=hs(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:p,offsetParent:g,strategy:a}):p);return{top:(h.top-C.top+y.top)/E.y,bottom:(C.bottom-h.bottom+y.bottom)/E.y,left:(h.left-C.left+y.left)/E.x,right:(C.right-h.right+y.right)/E.x}}const a1=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:l,middlewareData:a}=t,{element:u,padding:f=0}=Wt(e,t)||{};if(u==null)return{};const d=sm(f),c={x:n,y:r},m=Wu(o),y=Hu(m),v=await s.getDimensions(u),x=m==="y",h=x?"top":"left",p=x?"bottom":"right",g=x?"clientHeight":"clientWidth",E=i.reference[y]+i.reference[m]-c[m]-i.floating[y],C=c[m]-i.reference[m],b=await(s.getOffsetParent==null?void 0:s.getOffsetParent(u));let T=b?b[g]:0;(!T||!await(s.isElement==null?void 0:s.isElement(b)))&&(T=l.floating[g]||i.floating[y]);const P=E/2-C/2,j=T/2-v[y]/2-1,M=bn(d[h],j),F=bn(d[p],j),L=M,$=T-v[y]-F,A=T/2-v[y]/2+P,Q=ja(L,A,$),B=!a.arrow&&Gr(o)!=null&&A!==Q&&i.reference[y]/2-(A<L?M:F)-v[y]/2<0,K=B?A<L?A-L:A-$:0;return{[m]:c[m]+K,data:{[m]:Q,centerOffset:A-Q-K,...B&&{alignmentOffset:K}},reset:B}}}),u1=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:l,platform:a,elements:u}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:c,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:v=!0,...x}=Wt(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const h=Qt(o),p=Nn(l),g=Qt(l)===l,E=await(a.isRTL==null?void 0:a.isRTL(u.floating)),C=c||(g||!v?[ps(l)]:r1(l)),b=y!=="none";!c&&b&&C.push(...i1(l,v,y,E));const T=[l,...C],P=await $o(t,x),j=[];let M=((r=i.flip)==null?void 0:r.overflows)||[];if(f&&j.push(P[h]),d){const A=n1(o,s,E);j.push(P[A[0]],P[A[1]])}if(M=[...M,{placement:o,overflows:j}],!j.every(A=>A<=0)){var F,L;const A=(((F=i.flip)==null?void 0:F.index)||0)+1,Q=T[A];if(Q)return{data:{index:A,overflows:M},reset:{placement:Q}};let B=(L=M.filter(K=>K.overflows[0]<=0).sort((K,k)=>K.overflows[1]-k.overflows[1])[0])==null?void 0:L.placement;if(!B)switch(m){case"bestFit":{var $;const K=($=M.filter(k=>{if(b){const _=Nn(k.placement);return _===p||_==="y"}return!0}).map(k=>[k.placement,k.overflows.filter(_=>_>0).reduce((_,z)=>_+z,0)]).sort((k,_)=>k[1]-_[1])[0])==null?void 0:$[0];K&&(B=K);break}case"initialPlacement":B=l;break}if(o!==B)return{reset:{placement:B}}}return{}}}};function bd(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Td(e){return Jw.some(t=>e[t]>=0)}const c1=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Wt(e,t);switch(r){case"referenceHidden":{const i=await $o(t,{...o,elementContext:"reference"}),s=bd(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Td(s)}}}case"escaped":{const i=await $o(t,{...o,altBoundary:!0}),s=bd(i,n.floating);return{data:{escapedOffsets:s,escaped:Td(s)}}}default:return{}}}}};async function d1(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Qt(n),l=Gr(n),a=Nn(n)==="y",u=["left","top"].includes(s)?-1:1,f=i&&a?-1:1,d=Wt(t,e);let{mainAxis:c,crossAxis:m,alignmentAxis:y}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&typeof y=="number"&&(m=l==="end"?y*-1:y),a?{x:m*f,y:c*u}:{x:c*u,y:m*f}}const f1=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:l}=t,a=await d1(t,e);return s===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:s}}}}},p1=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:x=>{let{x:h,y:p}=x;return{x:h,y:p}}},...a}=Wt(e,t),u={x:n,y:r},f=await $o(t,a),d=Nn(Qt(o)),c=Vu(d);let m=u[c],y=u[d];if(i){const x=c==="y"?"top":"left",h=c==="y"?"bottom":"right",p=m+f[x],g=m-f[h];m=ja(p,m,g)}if(s){const x=d==="y"?"top":"left",h=d==="y"?"bottom":"right",p=y+f[x],g=y-f[h];y=ja(p,y,g)}const v=l.fn({...t,[c]:m,[d]:y});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[c]:i,[d]:s}}}}}},h1=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:l=0,mainAxis:a=!0,crossAxis:u=!0}=Wt(e,t),f={x:n,y:r},d=Nn(o),c=Vu(d);let m=f[c],y=f[d];const v=Wt(l,t),x=typeof v=="number"?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(a){const g=c==="y"?"height":"width",E=i.reference[c]-i.floating[g]+x.mainAxis,C=i.reference[c]+i.reference[g]-x.mainAxis;m<E?m=E:m>C&&(m=C)}if(u){var h,p;const g=c==="y"?"width":"height",E=["top","left"].includes(Qt(o)),C=i.reference[d]-i.floating[g]+(E&&((h=s.offset)==null?void 0:h[d])||0)+(E?0:x.crossAxis),b=i.reference[d]+i.reference[g]+(E?0:((p=s.offset)==null?void 0:p[d])||0)-(E?x.crossAxis:0);y<C?y=C:y>b&&(y=b)}return{[c]:m,[d]:y}}}},m1=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:l}=t,{apply:a=()=>{},...u}=Wt(e,t),f=await $o(t,u),d=Qt(o),c=Gr(o),m=Nn(o)==="y",{width:y,height:v}=i.floating;let x,h;d==="top"||d==="bottom"?(x=d,h=c===(await(s.isRTL==null?void 0:s.isRTL(l.floating))?"start":"end")?"left":"right"):(h=d,x=c==="end"?"top":"bottom");const p=v-f.top-f.bottom,g=y-f.left-f.right,E=bn(v-f[x],p),C=bn(y-f[h],g),b=!t.middlewareData.shift;let T=E,P=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=g),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(T=p),b&&!c){const M=Ke(f.left,0),F=Ke(f.right,0),L=Ke(f.top,0),$=Ke(f.bottom,0);m?P=y-2*(M!==0||F!==0?M+F:Ke(f.left,f.right)):T=v-2*(L!==0||$!==0?L+$:Ke(f.top,f.bottom))}await a({...t,availableWidth:P,availableHeight:T});const j=await s.getDimensions(l.floating);return y!==j.width||v!==j.height?{reset:{rects:!0}}:{}}}};function zs(){return typeof window<"u"}function Xr(e){return lm(e)?(e.nodeName||"").toLowerCase():"#document"}function Xe(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function jt(e){var t;return(t=(lm(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function lm(e){return zs()?e instanceof Node||e instanceof Xe(e).Node:!1}function wt(e){return zs()?e instanceof Element||e instanceof Xe(e).Element:!1}function Mt(e){return zs()?e instanceof HTMLElement||e instanceof Xe(e).HTMLElement:!1}function Nd(e){return!zs()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Xe(e).ShadowRoot}function Zo(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=xt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function g1(e){return["table","td","th"].includes(Xr(e))}function Fs(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Qu(e){const t=Ku(),n=wt(e)?xt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function v1(e){let t=Rn(e);for(;Mt(t)&&!Vr(t);){if(Qu(t))return t;if(Fs(t))return null;t=Rn(t)}return null}function Ku(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Vr(e){return["html","body","#document"].includes(Xr(e))}function xt(e){return Xe(e).getComputedStyle(e)}function $s(e){return wt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Rn(e){if(Xr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Nd(e)&&e.host||jt(e);return Nd(t)?t.host:t}function am(e){const t=Rn(e);return Vr(t)?e.ownerDocument?e.ownerDocument.body:e.body:Mt(t)&&Zo(t)?t:am(t)}function Uo(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=am(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=Xe(o);if(i){const l=La(s);return t.concat(s,s.visualViewport||[],Zo(o)?o:[],l&&n?Uo(l):[])}return t.concat(o,Uo(o,[],n))}function La(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function um(e){const t=xt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Mt(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,l=fs(n)!==i||fs(r)!==s;return l&&(n=i,r=s),{width:n,height:r,$:l}}function Yu(e){return wt(e)?e:e.contextElement}function Pr(e){const t=Yu(e);if(!Mt(t))return Tn(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=um(t);let s=(i?fs(n.width):n.width)/r,l=(i?fs(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}const y1=Tn(0);function cm(e){const t=Xe(e);return!Ku()||!t.visualViewport?y1:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function w1(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Xe(e)?!1:t}function qn(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=Yu(e);let s=Tn(1);t&&(r?wt(r)&&(s=Pr(r)):s=Pr(e));const l=w1(i,n,r)?cm(i):Tn(0);let a=(o.left+l.x)/s.x,u=(o.top+l.y)/s.y,f=o.width/s.x,d=o.height/s.y;if(i){const c=Xe(i),m=r&&wt(r)?Xe(r):r;let y=c,v=La(y);for(;v&&r&&m!==y;){const x=Pr(v),h=v.getBoundingClientRect(),p=xt(v),g=h.left+(v.clientLeft+parseFloat(p.paddingLeft))*x.x,E=h.top+(v.clientTop+parseFloat(p.paddingTop))*x.y;a*=x.x,u*=x.y,f*=x.x,d*=x.y,a+=g,u+=E,y=Xe(v),v=La(y)}}return hs({width:f,height:d,x:a,y:u})}function x1(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=jt(r),l=t?Fs(t.floating):!1;if(r===s||l&&i)return n;let a={scrollLeft:0,scrollTop:0},u=Tn(1);const f=Tn(0),d=Mt(r);if((d||!d&&!i)&&((Xr(r)!=="body"||Zo(s))&&(a=$s(r)),Mt(r))){const c=qn(r);u=Pr(r),f.x=c.x+r.clientLeft,f.y=c.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+f.x,y:n.y*u.y-a.scrollTop*u.y+f.y}}function S1(e){return Array.from(e.getClientRects())}function Ia(e,t){const n=$s(e).scrollLeft;return t?t.left+n:qn(jt(e)).left+n}function E1(e){const t=jt(e),n=$s(e),r=e.ownerDocument.body,o=Ke(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Ke(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+Ia(e);const l=-n.scrollTop;return xt(r).direction==="rtl"&&(s+=Ke(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:l}}function C1(e,t){const n=Xe(e),r=jt(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,l=0,a=0;if(o){i=o.width,s=o.height;const u=Ku();(!u||u&&t==="fixed")&&(l=o.offsetLeft,a=o.offsetTop)}return{width:i,height:s,x:l,y:a}}function k1(e,t){const n=qn(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Mt(e)?Pr(e):Tn(1),s=e.clientWidth*i.x,l=e.clientHeight*i.y,a=o*i.x,u=r*i.y;return{width:s,height:l,x:a,y:u}}function Rd(e,t,n){let r;if(t==="viewport")r=C1(e,n);else if(t==="document")r=E1(jt(e));else if(wt(t))r=k1(t,n);else{const o=cm(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return hs(r)}function dm(e,t){const n=Rn(e);return n===t||!wt(n)||Vr(n)?!1:xt(n).position==="fixed"||dm(n,t)}function P1(e,t){const n=t.get(e);if(n)return n;let r=Uo(e,[],!1).filter(l=>wt(l)&&Xr(l)!=="body"),o=null;const i=xt(e).position==="fixed";let s=i?Rn(e):e;for(;wt(s)&&!Vr(s);){const l=xt(s),a=Qu(s);!a&&l.position==="fixed"&&(o=null),(i?!a&&!o:!a&&l.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Zo(s)&&!a&&dm(e,s))?r=r.filter(f=>f!==s):o=l,s=Rn(s)}return t.set(e,r),r}function b1(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?Fs(t)?[]:P1(t,this._c):[].concat(n),r],l=s[0],a=s.reduce((u,f)=>{const d=Rd(t,f,o);return u.top=Ke(d.top,u.top),u.right=bn(d.right,u.right),u.bottom=bn(d.bottom,u.bottom),u.left=Ke(d.left,u.left),u},Rd(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function T1(e){const{width:t,height:n}=um(e);return{width:t,height:n}}function N1(e,t,n){const r=Mt(t),o=jt(t),i=n==="fixed",s=qn(e,!0,i,t);let l={scrollLeft:0,scrollTop:0};const a=Tn(0);if(r||!r&&!i)if((Xr(t)!=="body"||Zo(o))&&(l=$s(t)),r){const m=qn(t,!0,i,t);a.x=m.x+t.clientLeft,a.y=m.y+t.clientTop}else o&&(a.x=Ia(o));let u=0,f=0;if(o&&!r&&!i){const m=o.getBoundingClientRect();f=m.top+l.scrollTop,u=m.left+l.scrollLeft-Ia(o,m)}const d=s.left+l.scrollLeft-a.x-u,c=s.top+l.scrollTop-a.y-f;return{x:d,y:c,width:s.width,height:s.height}}function _l(e){return xt(e).position==="static"}function _d(e,t){if(!Mt(e)||xt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return jt(e)===n&&(n=n.ownerDocument.body),n}function fm(e,t){const n=Xe(e);if(Fs(e))return n;if(!Mt(e)){let o=Rn(e);for(;o&&!Vr(o);){if(wt(o)&&!_l(o))return o;o=Rn(o)}return n}let r=_d(e,t);for(;r&&g1(r)&&_l(r);)r=_d(r,t);return r&&Vr(r)&&_l(r)&&!Qu(r)?n:r||v1(e)||n}const R1=async function(e){const t=this.getOffsetParent||fm,n=this.getDimensions,r=await n(e.floating);return{reference:N1(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function _1(e){return xt(e).direction==="rtl"}const O1={convertOffsetParentRelativeRectToViewportRelativeRect:x1,getDocumentElement:jt,getClippingRect:b1,getOffsetParent:fm,getElementRects:R1,getClientRects:S1,getDimensions:T1,getScale:Pr,isElement:wt,isRTL:_1};function M1(e,t){let n=null,r;const o=jt(e);function i(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function s(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),i();const{left:u,top:f,width:d,height:c}=e.getBoundingClientRect();if(l||t(),!d||!c)return;const m=ki(f),y=ki(o.clientWidth-(u+d)),v=ki(o.clientHeight-(f+c)),x=ki(u),p={rootMargin:-m+"px "+-y+"px "+-v+"px "+-x+"px",threshold:Ke(0,bn(1,a))||1};let g=!0;function E(C){const b=C[0].intersectionRatio;if(b!==a){if(!g)return s();b?s(!1,b):r=setTimeout(()=>{s(!1,1e-7)},1e3)}g=!1}try{n=new IntersectionObserver(E,{...p,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,p)}n.observe(e)}return s(!0),i}function j1(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=Yu(e),f=o||i?[...u?Uo(u):[],...Uo(t)]:[];f.forEach(h=>{o&&h.addEventListener("scroll",n,{passive:!0}),i&&h.addEventListener("resize",n)});const d=u&&l?M1(u,n):null;let c=-1,m=null;s&&(m=new ResizeObserver(h=>{let[p]=h;p&&p.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(c),c=requestAnimationFrame(()=>{var g;(g=m)==null||g.observe(t)})),n()}),u&&!a&&m.observe(u),m.observe(t));let y,v=a?qn(e):null;a&&x();function x(){const h=qn(e);v&&(h.x!==v.x||h.y!==v.y||h.width!==v.width||h.height!==v.height)&&n(),v=h,y=requestAnimationFrame(x)}return n(),()=>{var h;f.forEach(p=>{o&&p.removeEventListener("scroll",n),i&&p.removeEventListener("resize",n)}),d==null||d(),(h=m)==null||h.disconnect(),m=null,a&&cancelAnimationFrame(y)}}const A1=f1,L1=p1,I1=u1,D1=m1,z1=c1,Od=a1,F1=h1,$1=(e,t,n)=>{const r=new Map,o={platform:O1,...n},i={...o.platform,_c:r};return l1(e,t,{...o,platform:i})};var $i=typeof document<"u"?w.useLayoutEffect:w.useEffect;function ms(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!ms(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!ms(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function pm(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Md(e,t){const n=pm(e);return Math.round(t*n)/n}function Ol(e){const t=w.useRef(e);return $i(()=>{t.current=e}),t}function U1(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:a,open:u}=e,[f,d]=w.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[c,m]=w.useState(r);ms(c,r)||m(r);const[y,v]=w.useState(null),[x,h]=w.useState(null),p=w.useCallback(k=>{k!==b.current&&(b.current=k,v(k))},[]),g=w.useCallback(k=>{k!==T.current&&(T.current=k,h(k))},[]),E=i||y,C=s||x,b=w.useRef(null),T=w.useRef(null),P=w.useRef(f),j=a!=null,M=Ol(a),F=Ol(o),L=Ol(u),$=w.useCallback(()=>{if(!b.current||!T.current)return;const k={placement:t,strategy:n,middleware:c};F.current&&(k.platform=F.current),$1(b.current,T.current,k).then(_=>{const z={..._,isPositioned:L.current!==!1};A.current&&!ms(P.current,z)&&(P.current=z,qo.flushSync(()=>{d(z)}))})},[c,t,n,F,L]);$i(()=>{u===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,d(k=>({...k,isPositioned:!1})))},[u]);const A=w.useRef(!1);$i(()=>(A.current=!0,()=>{A.current=!1}),[]),$i(()=>{if(E&&(b.current=E),C&&(T.current=C),E&&C){if(M.current)return M.current(E,C,$);$()}},[E,C,$,M,j]);const Q=w.useMemo(()=>({reference:b,floating:T,setReference:p,setFloating:g}),[p,g]),B=w.useMemo(()=>({reference:E,floating:C}),[E,C]),K=w.useMemo(()=>{const k={position:n,left:0,top:0};if(!B.floating)return k;const _=Md(B.floating,f.x),z=Md(B.floating,f.y);return l?{...k,transform:"translate("+_+"px, "+z+"px)",...pm(B.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:_,top:z}},[n,l,B.floating,f.x,f.y]);return w.useMemo(()=>({...f,update:$,refs:Q,elements:B,floatingStyles:K}),[f,$,Q,B,K])}const B1=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Od({element:r.current,padding:o}).fn(n):{}:r?Od({element:r,padding:o}).fn(n):{}}}},V1=(e,t)=>({...A1(e),options:[e,t]}),H1=(e,t)=>({...L1(e),options:[e,t]}),W1=(e,t)=>({...F1(e),options:[e,t]}),Q1=(e,t)=>({...I1(e),options:[e,t]}),K1=(e,t)=>({...D1(e),options:[e,t]}),Y1=(e,t)=>({...z1(e),options:[e,t]}),G1=(e,t)=>({...B1(e),options:[e,t]});var X1="Arrow",hm=w.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return S.jsx(Be.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:S.jsx("polygon",{points:"0,0 30,0 15,10"})})});hm.displayName=X1;var q1=hm;function Z1(e,t=[]){let n=[];function r(i,s){const l=w.createContext(s),a=n.length;n=[...n,s];function u(d){const{scope:c,children:m,...y}=d,v=(c==null?void 0:c[e][a])||l,x=w.useMemo(()=>y,Object.values(y));return S.jsx(v.Provider,{value:x,children:m})}function f(d,c){const m=(c==null?void 0:c[e][a])||l,y=w.useContext(m);if(y)return y;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,f]}const o=()=>{const i=n.map(s=>w.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return w.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,J1(o,...t)]}function J1(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const d=a(i)[`__scope${u}`];return{...l,...d}},{});return w.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function ex(e){const[t,n]=w.useState(void 0);return Xn(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,l;if("borderBoxSize"in i){const a=i.borderBoxSize,u=Array.isArray(a)?a[0]:a;s=u.inlineSize,l=u.blockSize}else s=e.offsetWidth,l=e.offsetHeight;n({width:s,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var mm="Popper",[gm,vm]=Z1(mm),[YS,ym]=gm(mm),wm="PopperAnchor",xm=w.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=ym(wm,n),s=w.useRef(null),l=yt(t,s);return w.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:S.jsx(Be.div,{...o,ref:l})});xm.displayName=wm;var Gu="PopperContent",[tx,nx]=gm(Gu),Sm=w.forwardRef((e,t)=>{var at,Yt,Gt,Xt,Jo,tr;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:l=0,avoidCollisions:a=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:d="partial",hideWhenDetached:c=!1,updatePositionStrategy:m="optimized",onPlaced:y,...v}=e,x=ym(Gu,n),[h,p]=w.useState(null),g=yt(t,jn=>p(jn)),[E,C]=w.useState(null),b=ex(E),T=(b==null?void 0:b.width)??0,P=(b==null?void 0:b.height)??0,j=r+(i!=="center"?"-"+i:""),M=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},F=Array.isArray(u)?u:[u],L=F.length>0,$={padding:M,boundary:F.filter(ox),altBoundary:L},{refs:A,floatingStyles:Q,placement:B,isPositioned:K,middlewareData:k}=U1({strategy:"fixed",placement:j,whileElementsMounted:(...jn)=>j1(...jn,{animationFrame:m==="always"}),elements:{reference:x.anchor},middleware:[V1({mainAxis:o+P,alignmentAxis:s}),a&&H1({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?W1():void 0,...$}),a&&Q1({...$}),K1({...$,apply:({elements:jn,rects:qr,availableWidth:ei,availableHeight:qt})=>{const{width:Ys,height:Gs}=qr.reference,xe=jn.floating.style;xe.setProperty("--radix-popper-available-width",`${ei}px`),xe.setProperty("--radix-popper-available-height",`${qt}px`),xe.setProperty("--radix-popper-anchor-width",`${Ys}px`),xe.setProperty("--radix-popper-anchor-height",`${Gs}px`)}}),E&&G1({element:E,padding:l}),ix({arrowWidth:T,arrowHeight:P}),c&&Y1({strategy:"referenceHidden",...$})]}),[_,z]=km(B),D=Ot(y);Xn(()=>{K&&(D==null||D())},[K,D]);const U=(at=k.arrow)==null?void 0:at.x,G=(Yt=k.arrow)==null?void 0:Yt.y,le=((Gt=k.arrow)==null?void 0:Gt.centerOffset)!==0,[Ve,Z]=w.useState();return Xn(()=>{h&&Z(window.getComputedStyle(h).zIndex)},[h]),S.jsx("div",{ref:A.setFloating,"data-radix-popper-content-wrapper":"",style:{...Q,transform:K?Q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ve,"--radix-popper-transform-origin":[(Xt=k.transformOrigin)==null?void 0:Xt.x,(Jo=k.transformOrigin)==null?void 0:Jo.y].join(" "),...((tr=k.hide)==null?void 0:tr.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:S.jsx(tx,{scope:n,placedSide:_,onArrowChange:C,arrowX:U,arrowY:G,shouldHideArrow:le,children:S.jsx(Be.div,{"data-side":_,"data-align":z,...v,ref:g,style:{...v.style,animation:K?void 0:"none"}})})})});Sm.displayName=Gu;var Em="PopperArrow",rx={top:"bottom",right:"left",bottom:"top",left:"right"},Cm=w.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=nx(Em,r),s=rx[i.placedSide];return S.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:S.jsx(q1,{...o,ref:n,style:{...o.style,display:"block"}})})});Cm.displayName=Em;function ox(e){return e!==null}var ix=e=>({name:"transformOrigin",options:e,fn(t){var x,h,p;const{placement:n,rects:r,middlewareData:o}=t,s=((x=o.arrow)==null?void 0:x.centerOffset)!==0,l=s?0:e.arrowWidth,a=s?0:e.arrowHeight,[u,f]=km(n),d={start:"0%",center:"50%",end:"100%"}[f],c=(((h=o.arrow)==null?void 0:h.x)??0)+l/2,m=(((p=o.arrow)==null?void 0:p.y)??0)+a/2;let y="",v="";return u==="bottom"?(y=s?d:`${c}px`,v=`${-a}px`):u==="top"?(y=s?d:`${c}px`,v=`${r.floating.height+a}px`):u==="right"?(y=`${-a}px`,v=s?d:`${m}px`):u==="left"&&(y=`${r.floating.width+a}px`,v=s?d:`${m}px`),{data:{x:y,y:v}}}});function km(e){const[t,n="center"]=e.split("-");return[t,n]}var sx=xm,lx=Sm,ax=Cm,[Us,GS]=Sh("Tooltip",[vm]),Xu=vm(),Pm="TooltipProvider",ux=700,jd="tooltip.open",[cx,bm]=Us(Pm),Tm=e=>{const{__scopeTooltip:t,delayDuration:n=ux,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,[s,l]=w.useState(!0),a=w.useRef(!1),u=w.useRef(0);return w.useEffect(()=>{const f=u.current;return()=>window.clearTimeout(f)},[]),S.jsx(cx,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:w.useCallback(()=>{window.clearTimeout(u.current),l(!1)},[]),onClose:w.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l(!0),r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:w.useCallback(f=>{a.current=f},[]),disableHoverableContent:o,children:i})};Tm.displayName=Pm;var Nm="Tooltip",[XS,Bs]=Us(Nm),Da="TooltipTrigger",dx=w.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Bs(Da,n),i=bm(Da,n),s=Xu(n),l=w.useRef(null),a=yt(t,l,o.onTriggerChange),u=w.useRef(!1),f=w.useRef(!1),d=w.useCallback(()=>u.current=!1,[]);return w.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),S.jsx(sx,{asChild:!0,...s,children:S.jsx(Be.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:a,onPointerMove:ge(e.onPointerMove,c=>{c.pointerType!=="touch"&&!f.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:ge(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:ge(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:ge(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ge(e.onBlur,o.onClose),onClick:ge(e.onClick,o.onClose)})})});dx.displayName=Da;var fx="TooltipPortal",[qS,px]=Us(fx,{forceMount:void 0}),Hr="TooltipContent",Rm=w.forwardRef((e,t)=>{const n=px(Hr,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,s=Bs(Hr,e.__scopeTooltip);return S.jsx(zu,{present:r||s.open,children:s.disableHoverableContent?S.jsx(_m,{side:o,...i,ref:t}):S.jsx(hx,{side:o,...i,ref:t})})}),hx=w.forwardRef((e,t)=>{const n=Bs(Hr,e.__scopeTooltip),r=bm(Hr,e.__scopeTooltip),o=w.useRef(null),i=yt(t,o),[s,l]=w.useState(null),{trigger:a,onClose:u}=n,f=o.current,{onPointerInTransitChange:d}=r,c=w.useCallback(()=>{l(null),d(!1)},[d]),m=w.useCallback((y,v)=>{const x=y.currentTarget,h={x:y.clientX,y:y.clientY},p=yx(h,x.getBoundingClientRect()),g=wx(h,p),E=xx(v.getBoundingClientRect()),C=Ex([...g,...E]);l(C),d(!0)},[d]);return w.useEffect(()=>()=>c(),[c]),w.useEffect(()=>{if(a&&f){const y=x=>m(x,f),v=x=>m(x,a);return a.addEventListener("pointerleave",y),f.addEventListener("pointerleave",v),()=>{a.removeEventListener("pointerleave",y),f.removeEventListener("pointerleave",v)}}},[a,f,m,c]),w.useEffect(()=>{if(s){const y=v=>{const x=v.target,h={x:v.clientX,y:v.clientY},p=(a==null?void 0:a.contains(x))||(f==null?void 0:f.contains(x)),g=!Sx(h,s);p?c():g&&(c(),u())};return document.addEventListener("pointermove",y),()=>document.removeEventListener("pointermove",y)}},[a,f,s,u,c]),S.jsx(_m,{...e,ref:i})}),[mx,gx]=Us(Nm,{isInside:!1}),_m=w.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s,...l}=e,a=Bs(Hr,n),u=Xu(n),{onClose:f}=a;return w.useEffect(()=>(document.addEventListener(jd,f),()=>document.removeEventListener(jd,f)),[f]),w.useEffect(()=>{if(a.trigger){const d=c=>{const m=c.target;m!=null&&m.contains(a.trigger)&&f()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[a.trigger,f]),S.jsx(Du,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:d=>d.preventDefault(),onDismiss:f,children:S.jsxs(lx,{"data-state":a.stateAttribute,...u,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[S.jsx(xh,{children:r}),S.jsx(mx,{scope:n,isInside:!0,children:S.jsx(ay,{id:a.contentId,role:"tooltip",children:o||r})})]})})});Rm.displayName=Hr;var Om="TooltipArrow",vx=w.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Xu(n);return gx(Om,n).isInside?null:S.jsx(ax,{...o,...r,ref:t})});vx.displayName=Om;function yx(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function wx(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function xx(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Sx(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const l=t[i].x,a=t[i].y,u=t[s].x,f=t[s].y;a>r!=f>r&&n<(u-l)*(r-a)/(f-a)+l&&(o=!o)}return o}function Ex(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Cx(t)}function Cx(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],s=t[t.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],s=n[n.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var kx=Tm,Mm=Rm;const Px=kx,bx=w.forwardRef(({className:e,sideOffset:t=4,...n},r)=>S.jsx(Mm,{ref:r,sideOffset:t,className:er("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));bx.displayName=Mm.displayName;var Vs=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Hs=typeof window>"u"||"Deno"in globalThis;function dt(){}function Tx(e,t){return typeof e=="function"?e(t):e}function Nx(e){return typeof e=="number"&&e>=0&&e!==1/0}function Rx(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Ad(e,t){return typeof e=="function"?e(t):e}function _x(e,t){return typeof e=="function"?e(t):e}function Ld(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:i,queryKey:s,stale:l}=e;if(s){if(r){if(t.queryHash!==qu(s,t.options))return!1}else if(!Vo(t.queryKey,s))return!1}if(n!=="all"){const a=t.isActive();if(n==="active"&&!a||n==="inactive"&&a)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||o&&o!==t.state.fetchStatus||i&&!i(t))}function Id(e,t){const{exact:n,status:r,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(Bo(t.options.mutationKey)!==Bo(i))return!1}else if(!Vo(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function qu(e,t){return((t==null?void 0:t.queryKeyHashFn)||Bo)(e)}function Bo(e){return JSON.stringify(e,(t,n)=>za(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function Vo(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Vo(e[n],t[n])):!1}function jm(e,t){if(e===t)return e;const n=Dd(e)&&Dd(t);if(n||za(e)&&za(t)){const r=n?e:Object.keys(e),o=r.length,i=n?t:Object.keys(t),s=i.length,l=n?[]:{};let a=0;for(let u=0;u<s;u++){const f=n?u:i[u];(!n&&r.includes(f)||n)&&e[f]===void 0&&t[f]===void 0?(l[f]=void 0,a++):(l[f]=jm(e[f],t[f]),l[f]===e[f]&&e[f]!==void 0&&a++)}return o===s&&a===o?e:l}return t}function Dd(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function za(e){if(!zd(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!zd(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function zd(e){return Object.prototype.toString.call(e)==="[object Object]"}function Ox(e){return new Promise(t=>{setTimeout(t,e)})}function Mx(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?jm(e,t):t}function jx(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function Ax(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var Zu=Symbol();function Am(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===Zu?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var Fn,an,Tr,Gd,Lx=(Gd=class extends Vs{constructor(){super();q(this,Fn);q(this,an);q(this,Tr);W(this,Tr,t=>{if(!Hs&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){N(this,an)||this.setEventListener(N(this,Tr))}onUnsubscribe(){var t;this.hasListeners()||((t=N(this,an))==null||t.call(this),W(this,an,void 0))}setEventListener(t){var n;W(this,Tr,t),(n=N(this,an))==null||n.call(this),W(this,an,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){N(this,Fn)!==t&&(W(this,Fn,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof N(this,Fn)=="boolean"?N(this,Fn):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Fn=new WeakMap,an=new WeakMap,Tr=new WeakMap,Gd),Lm=new Lx,Nr,un,Rr,Xd,Ix=(Xd=class extends Vs{constructor(){super();q(this,Nr,!0);q(this,un);q(this,Rr);W(this,Rr,t=>{if(!Hs&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){N(this,un)||this.setEventListener(N(this,Rr))}onUnsubscribe(){var t;this.hasListeners()||((t=N(this,un))==null||t.call(this),W(this,un,void 0))}setEventListener(t){var n;W(this,Rr,t),(n=N(this,un))==null||n.call(this),W(this,un,t(this.setOnline.bind(this)))}setOnline(t){N(this,Nr)!==t&&(W(this,Nr,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return N(this,Nr)}},Nr=new WeakMap,un=new WeakMap,Rr=new WeakMap,Xd),gs=new Ix;function Dx(){let e,t;const n=new Promise((o,i)=>{e=o,t=i});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function zx(e){return Math.min(1e3*2**e,3e4)}function Im(e){return(e??"online")==="online"?gs.isOnline():!0}var Dm=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Ml(e){return e instanceof Dm}function zm(e){let t=!1,n=0,r=!1,o;const i=Dx(),s=v=>{var x;r||(c(new Dm(v)),(x=e.abort)==null||x.call(e))},l=()=>{t=!0},a=()=>{t=!1},u=()=>Lm.isFocused()&&(e.networkMode==="always"||gs.isOnline())&&e.canRun(),f=()=>Im(e.networkMode)&&e.canRun(),d=v=>{var x;r||(r=!0,(x=e.onSuccess)==null||x.call(e,v),o==null||o(),i.resolve(v))},c=v=>{var x;r||(r=!0,(x=e.onError)==null||x.call(e,v),o==null||o(),i.reject(v))},m=()=>new Promise(v=>{var x;o=h=>{(r||u())&&v(h)},(x=e.onPause)==null||x.call(e)}).then(()=>{var v;o=void 0,r||(v=e.onContinue)==null||v.call(e)}),y=()=>{if(r)return;let v;const x=n===0?e.initialPromise:void 0;try{v=x??e.fn()}catch(h){v=Promise.reject(h)}Promise.resolve(v).then(d).catch(h=>{var b;if(r)return;const p=e.retry??(Hs?0:3),g=e.retryDelay??zx,E=typeof g=="function"?g(n,h):g,C=p===!0||typeof p=="number"&&n<p||typeof p=="function"&&p(n,h);if(t||!C){c(h);return}n++,(b=e.onFail)==null||b.call(e,n,h),Ox(E).then(()=>u()?void 0:m()).then(()=>{t?c(h):y()})})};return{promise:i,cancel:s,continue:()=>(o==null||o(),i),cancelRetry:l,continueRetry:a,canStart:f,start:()=>(f()?y():m().then(y),i)}}function Fx(){let e=[],t=0,n=l=>{l()},r=l=>{l()},o=l=>setTimeout(l,0);const i=l=>{t?e.push(l):o(()=>{n(l)})},s=()=>{const l=e;e=[],l.length&&o(()=>{r(()=>{l.forEach(a=>{n(a)})})})};return{batch:l=>{let a;t++;try{a=l()}finally{t--,t||s()}return a},batchCalls:l=>(...a)=>{i(()=>{l(...a)})},schedule:i,setNotifyFunction:l=>{n=l},setBatchNotifyFunction:l=>{r=l},setScheduler:l=>{o=l}}}var Me=Fx(),$n,qd,Fm=(qd=class{constructor(){q(this,$n)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Nx(this.gcTime)&&W(this,$n,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Hs?1/0:5*60*1e3))}clearGcTimeout(){N(this,$n)&&(clearTimeout(N(this,$n)),W(this,$n,void 0))}},$n=new WeakMap,qd),_r,Or,tt,Te,Ho,Un,ft,Lt,Zd,$x=(Zd=class extends Fm{constructor(t){super();q(this,ft);q(this,_r);q(this,Or);q(this,tt);q(this,Te);q(this,Ho);q(this,Un);W(this,Un,!1),W(this,Ho,t.defaultOptions),this.setOptions(t.options),this.observers=[],W(this,tt,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,W(this,_r,Bx(this.options)),this.state=t.state??N(this,_r),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=N(this,Te))==null?void 0:t.promise}setOptions(t){this.options={...N(this,Ho),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&N(this,tt).remove(this)}setData(t,n){const r=Mx(this.state.data,t,this.options);return ke(this,ft,Lt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){ke(this,ft,Lt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=N(this,Te))==null?void 0:r.promise;return(o=N(this,Te))==null||o.cancel(t),n?n.then(dt).catch(dt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(N(this,_r))}isActive(){return this.observers.some(t=>_x(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Zu||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!Rx(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=N(this,Te))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=N(this,Te))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),N(this,tt).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(N(this,Te)&&(N(this,Un)?N(this,Te).cancel({revert:!0}):N(this,Te).cancelRetry()),this.scheduleGc()),N(this,tt).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||ke(this,ft,Lt).call(this,{type:"invalidate"})}fetch(t,n){var a,u,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(N(this,Te))return N(this,Te).continueRetry(),N(this,Te).promise}if(t&&this.setOptions(t),!this.options.queryFn){const d=this.observers.find(c=>c.options.queryFn);d&&this.setOptions(d.options)}const r=new AbortController,o=d=>{Object.defineProperty(d,"signal",{enumerable:!0,get:()=>(W(this,Un,!0),r.signal)})},i=()=>{const d=Am(this.options,n),c={queryKey:this.queryKey,meta:this.meta};return o(c),W(this,Un,!1),this.options.persister?this.options.persister(d,c,this):d(c)},s={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:i};o(s),(a=this.options.behavior)==null||a.onFetch(s,this),W(this,Or,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=s.fetchOptions)==null?void 0:u.meta))&&ke(this,ft,Lt).call(this,{type:"fetch",meta:(f=s.fetchOptions)==null?void 0:f.meta});const l=d=>{var c,m,y,v;Ml(d)&&d.silent||ke(this,ft,Lt).call(this,{type:"error",error:d}),Ml(d)||((m=(c=N(this,tt).config).onError)==null||m.call(c,d,this),(v=(y=N(this,tt).config).onSettled)==null||v.call(y,this.state.data,d,this)),this.scheduleGc()};return W(this,Te,zm({initialPromise:n==null?void 0:n.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:d=>{var c,m,y,v;if(d===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(d)}catch(x){l(x);return}(m=(c=N(this,tt).config).onSuccess)==null||m.call(c,d,this),(v=(y=N(this,tt).config).onSettled)==null||v.call(y,d,this.state.error,this),this.scheduleGc()},onError:l,onFail:(d,c)=>{ke(this,ft,Lt).call(this,{type:"failed",failureCount:d,error:c})},onPause:()=>{ke(this,ft,Lt).call(this,{type:"pause"})},onContinue:()=>{ke(this,ft,Lt).call(this,{type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0})),N(this,Te).start()}},_r=new WeakMap,Or=new WeakMap,tt=new WeakMap,Te=new WeakMap,Ho=new WeakMap,Un=new WeakMap,ft=new WeakSet,Lt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...Ux(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return Ml(o)&&o.revert&&N(this,Or)?{...N(this,Or),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Me.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),N(this,tt).notify({query:this,type:"updated",action:t})})},Zd);function Ux(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Im(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function Bx(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Pt,Jd,Vx=(Jd=class extends Vs{constructor(t={}){super();q(this,Pt);this.config=t,W(this,Pt,new Map)}build(t,n,r){const o=n.queryKey,i=n.queryHash??qu(o,n);let s=this.get(i);return s||(s=new $x({cache:this,queryKey:o,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(s)),s}add(t){N(this,Pt).has(t.queryHash)||(N(this,Pt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=N(this,Pt).get(t.queryHash);n&&(t.destroy(),n===t&&N(this,Pt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Me.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return N(this,Pt).get(t)}getAll(){return[...N(this,Pt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Ld(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>Ld(t,r)):n}notify(t){Me.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Me.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Me.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Pt=new WeakMap,Jd),bt,_e,Bn,Tt,nn,ef,Hx=(ef=class extends Fm{constructor(t){super();q(this,Tt);q(this,bt);q(this,_e);q(this,Bn);this.mutationId=t.mutationId,W(this,_e,t.mutationCache),W(this,bt,[]),this.state=t.state||Wx(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){N(this,bt).includes(t)||(N(this,bt).push(t),this.clearGcTimeout(),N(this,_e).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){W(this,bt,N(this,bt).filter(n=>n!==t)),this.scheduleGc(),N(this,_e).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){N(this,bt).length||(this.state.status==="pending"?this.scheduleGc():N(this,_e).remove(this))}continue(){var t;return((t=N(this,Bn))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,i,s,l,a,u,f,d,c,m,y,v,x,h,p,g,E,C,b,T;W(this,Bn,zm({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(P,j)=>{ke(this,Tt,nn).call(this,{type:"failed",failureCount:P,error:j})},onPause:()=>{ke(this,Tt,nn).call(this,{type:"pause"})},onContinue:()=>{ke(this,Tt,nn).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>N(this,_e).canRun(this)}));const n=this.state.status==="pending",r=!N(this,Bn).canStart();try{if(!n){ke(this,Tt,nn).call(this,{type:"pending",variables:t,isPaused:r}),await((i=(o=N(this,_e).config).onMutate)==null?void 0:i.call(o,t,this));const j=await((l=(s=this.options).onMutate)==null?void 0:l.call(s,t));j!==this.state.context&&ke(this,Tt,nn).call(this,{type:"pending",context:j,variables:t,isPaused:r})}const P=await N(this,Bn).start();return await((u=(a=N(this,_e).config).onSuccess)==null?void 0:u.call(a,P,t,this.state.context,this)),await((d=(f=this.options).onSuccess)==null?void 0:d.call(f,P,t,this.state.context)),await((m=(c=N(this,_e).config).onSettled)==null?void 0:m.call(c,P,null,this.state.variables,this.state.context,this)),await((v=(y=this.options).onSettled)==null?void 0:v.call(y,P,null,t,this.state.context)),ke(this,Tt,nn).call(this,{type:"success",data:P}),P}catch(P){try{throw await((h=(x=N(this,_e).config).onError)==null?void 0:h.call(x,P,t,this.state.context,this)),await((g=(p=this.options).onError)==null?void 0:g.call(p,P,t,this.state.context)),await((C=(E=N(this,_e).config).onSettled)==null?void 0:C.call(E,void 0,P,this.state.variables,this.state.context,this)),await((T=(b=this.options).onSettled)==null?void 0:T.call(b,void 0,P,t,this.state.context)),P}finally{ke(this,Tt,nn).call(this,{type:"error",error:P})}}finally{N(this,_e).runNext(this)}}},bt=new WeakMap,_e=new WeakMap,Bn=new WeakMap,Tt=new WeakSet,nn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Me.batch(()=>{N(this,bt).forEach(r=>{r.onMutationUpdate(t)}),N(this,_e).notify({mutation:this,type:"updated",action:t})})},ef);function Wx(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var He,Wo,tf,Qx=(tf=class extends Vs{constructor(t={}){super();q(this,He);q(this,Wo);this.config=t,W(this,He,new Map),W(this,Wo,Date.now())}build(t,n,r){const o=new Hx({mutationCache:this,mutationId:++ri(this,Wo)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=Pi(t),r=N(this,He).get(n)??[];r.push(t),N(this,He).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=Pi(t);if(N(this,He).has(n)){const o=(r=N(this,He).get(n))==null?void 0:r.filter(i=>i!==t);o&&(o.length===0?N(this,He).delete(n):N(this,He).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=N(this,He).get(Pi(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=N(this,He).get(Pi(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){Me.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...N(this,He).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Id(n,r))}findAll(t={}){return this.getAll().filter(n=>Id(t,n))}notify(t){Me.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Me.batch(()=>Promise.all(t.map(n=>n.continue().catch(dt))))}},He=new WeakMap,Wo=new WeakMap,tf);function Pi(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function Fd(e){return{onFetch:(t,n)=>{var f,d,c,m,y;const r=t.options,o=(c=(d=(f=t.fetchOptions)==null?void 0:f.meta)==null?void 0:d.fetchMore)==null?void 0:c.direction,i=((m=t.state.data)==null?void 0:m.pages)||[],s=((y=t.state.data)==null?void 0:y.pageParams)||[];let l={pages:[],pageParams:[]},a=0;const u=async()=>{let v=!1;const x=g=>{Object.defineProperty(g,"signal",{enumerable:!0,get:()=>(t.signal.aborted?v=!0:t.signal.addEventListener("abort",()=>{v=!0}),t.signal)})},h=Am(t.options,t.fetchOptions),p=async(g,E,C)=>{if(v)return Promise.reject();if(E==null&&g.pages.length)return Promise.resolve(g);const b={queryKey:t.queryKey,pageParam:E,direction:C?"backward":"forward",meta:t.options.meta};x(b);const T=await h(b),{maxPages:P}=t.options,j=C?Ax:jx;return{pages:j(g.pages,T,P),pageParams:j(g.pageParams,E,P)}};if(o&&i.length){const g=o==="backward",E=g?Kx:$d,C={pages:i,pageParams:s},b=E(r,C);l=await p(C,b,g)}else{const g=e??i.length;do{const E=a===0?s[0]??r.initialPageParam:$d(r,l);if(a>0&&E==null)break;l=await p(l,E),a++}while(a<g)}return l};t.options.persister?t.fetchFn=()=>{var v,x;return(x=(v=t.options).persister)==null?void 0:x.call(v,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function $d(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function Kx(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var de,cn,dn,Mr,jr,fn,Ar,Lr,nf,Yx=(nf=class{constructor(e={}){q(this,de);q(this,cn);q(this,dn);q(this,Mr);q(this,jr);q(this,fn);q(this,Ar);q(this,Lr);W(this,de,e.queryCache||new Vx),W(this,cn,e.mutationCache||new Qx),W(this,dn,e.defaultOptions||{}),W(this,Mr,new Map),W(this,jr,new Map),W(this,fn,0)}mount(){ri(this,fn)._++,N(this,fn)===1&&(W(this,Ar,Lm.subscribe(async e=>{e&&(await this.resumePausedMutations(),N(this,de).onFocus())})),W(this,Lr,gs.subscribe(async e=>{e&&(await this.resumePausedMutations(),N(this,de).onOnline())})))}unmount(){var e,t;ri(this,fn)._--,N(this,fn)===0&&((e=N(this,Ar))==null||e.call(this),W(this,Ar,void 0),(t=N(this,Lr))==null||t.call(this),W(this,Lr,void 0))}isFetching(e){return N(this,de).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return N(this,cn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=N(this,de).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=N(this,de).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(Ad(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return N(this,de).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=N(this,de).get(r.queryHash),i=o==null?void 0:o.state.data,s=Tx(t,i);if(s!==void 0)return N(this,de).build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return Me.batch(()=>N(this,de).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=N(this,de).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=N(this,de);Me.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=N(this,de),r={type:"active",...e};return Me.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Me.batch(()=>N(this,de).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(dt).catch(dt)}invalidateQueries(e={},t={}){return Me.batch(()=>{if(N(this,de).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Me.batch(()=>N(this,de).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let i=o.fetch(void 0,n);return n.throwOnError||(i=i.catch(dt)),o.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(dt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=N(this,de).build(this,t);return n.isStaleByTime(Ad(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(dt).catch(dt)}fetchInfiniteQuery(e){return e.behavior=Fd(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(dt).catch(dt)}ensureInfiniteQueryData(e){return e.behavior=Fd(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return gs.isOnline()?N(this,cn).resumePausedMutations():Promise.resolve()}getQueryCache(){return N(this,de)}getMutationCache(){return N(this,cn)}getDefaultOptions(){return N(this,dn)}setDefaultOptions(e){W(this,dn,e)}setQueryDefaults(e,t){N(this,Mr).set(Bo(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...N(this,Mr).values()];let n={};return t.forEach(r=>{Vo(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){N(this,jr).set(Bo(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...N(this,jr).values()];let n={};return t.forEach(r=>{Vo(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...N(this,dn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=qu(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===Zu&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...N(this,dn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){N(this,de).clear(),N(this,cn).clear()}},de=new WeakMap,cn=new WeakMap,dn=new WeakMap,Mr=new WeakMap,jr=new WeakMap,fn=new WeakMap,Ar=new WeakMap,Lr=new WeakMap,nf),Gx=w.createContext(void 0),Xx=({client:e,children:t})=>(w.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),S.jsx(Gx.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function vs(){return vs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},vs.apply(this,arguments)}var mn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(mn||(mn={}));const Ud="popstate";function qx(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:l}=r.location;return Fa("",{pathname:i,search:s,hash:l},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Um(o)}return Jx(t,n,null,e)}function Ue(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function $m(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Zx(){return Math.random().toString(36).substr(2,8)}function Bd(e,t){return{usr:e.state,key:e.key,idx:t}}function Fa(e,t,n,r){return n===void 0&&(n=null),vs({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Ws(t):t,{state:n,key:t&&t.key||r||Zx()})}function Um(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Ws(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Jx(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,l=mn.Pop,a=null,u=f();u==null&&(u=0,s.replaceState(vs({},s.state,{idx:u}),""));function f(){return(s.state||{idx:null}).idx}function d(){l=mn.Pop;let x=f(),h=x==null?null:x-u;u=x,a&&a({action:l,location:v.location,delta:h})}function c(x,h){l=mn.Push;let p=Fa(v.location,x,h);u=f()+1;let g=Bd(p,u),E=v.createHref(p);try{s.pushState(g,"",E)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(E)}i&&a&&a({action:l,location:v.location,delta:1})}function m(x,h){l=mn.Replace;let p=Fa(v.location,x,h);u=f();let g=Bd(p,u),E=v.createHref(p);s.replaceState(g,"",E),i&&a&&a({action:l,location:v.location,delta:0})}function y(x){let h=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof x=="string"?x:Um(x);return p=p.replace(/ $/,"%20"),Ue(h,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,h)}let v={get action(){return l},get location(){return e(o,s)},listen(x){if(a)throw new Error("A history only accepts one active listener");return o.addEventListener(Ud,d),a=x,()=>{o.removeEventListener(Ud,d),a=null}},createHref(x){return t(o,x)},createURL:y,encodeLocation(x){let h=y(x);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:c,replace:m,go(x){return s.go(x)}};return v}var Vd;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Vd||(Vd={}));function eS(e,t,n){return n===void 0&&(n="/"),tS(e,t,n,!1)}function tS(e,t,n,r){let o=typeof t=="string"?Ws(t):t,i=Hm(o.pathname||"/",n);if(i==null)return null;let s=Bm(e);nS(s);let l=null;for(let a=0;l==null&&a<s.length;++a){let u=pS(i);l=dS(s[a],u,r)}return l}function Bm(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,l)=>{let a={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};a.relativePath.startsWith("/")&&(Ue(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=br([r,a.relativePath]),f=n.concat(a);i.children&&i.children.length>0&&(Ue(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Bm(i.children,t,f,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:uS(u,i.index),routesMeta:f})};return e.forEach((i,s)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))o(i,s);else for(let a of Vm(i.path))o(i,s,a)}),t}function Vm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=Vm(r.join("/")),l=[];return l.push(...s.map(a=>a===""?i:[i,a].join("/"))),o&&l.push(...s),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function nS(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:cS(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const rS=/^:[\w-]+$/,oS=3,iS=2,sS=1,lS=10,aS=-2,Hd=e=>e==="*";function uS(e,t){let n=e.split("/"),r=n.length;return n.some(Hd)&&(r+=aS),t&&(r+=iS),n.filter(o=>!Hd(o)).reduce((o,i)=>o+(rS.test(i)?oS:i===""?sS:lS),r)}function cS(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function dS(e,t,n){let{routesMeta:r}=e,o={},i="/",s=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,f=i==="/"?t:t.slice(i.length)||"/",d=Wd({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},f),c=a.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=Wd({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},f)),!d)return null;Object.assign(o,d.params),s.push({params:o,pathname:br([i,d.pathname]),pathnameBase:hS(br([i,d.pathnameBase])),route:c}),d.pathnameBase!=="/"&&(i=br([i,d.pathnameBase]))}return s}function Wd(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=fS(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce((u,f,d)=>{let{paramName:c,isOptional:m}=f;if(c==="*"){let v=l[d]||"";s=i.slice(0,i.length-v.length).replace(/(.)\/+$/,"$1")}const y=l[d];return m&&!y?u[c]=void 0:u[c]=(y||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function fS(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),$m(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function pS(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return $m(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Hm(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}const br=e=>e.join("/").replace(/\/\/+/g,"/"),hS=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function mS(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Wm=["post","put","patch","delete"];new Set(Wm);const gS=["get",...Wm];new Set(gS);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ys(){return ys=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ys.apply(this,arguments)}const vS=w.createContext(null),yS=w.createContext(null),Qm=w.createContext(null),Qs=w.createContext(null),Ks=w.createContext({outlet:null,matches:[],isDataRoute:!1}),Km=w.createContext(null);function Ju(){return w.useContext(Qs)!=null}function Ym(){return Ju()||Ue(!1),w.useContext(Qs).location}function wS(e,t){return xS(e,t)}function xS(e,t,n,r){Ju()||Ue(!1);let{navigator:o}=w.useContext(Qm),{matches:i}=w.useContext(Ks),s=i[i.length-1],l=s?s.params:{};s&&s.pathname;let a=s?s.pathnameBase:"/";s&&s.route;let u=Ym(),f;if(t){var d;let x=typeof t=="string"?Ws(t):t;a==="/"||(d=x.pathname)!=null&&d.startsWith(a)||Ue(!1),f=x}else f=u;let c=f.pathname||"/",m=c;if(a!=="/"){let x=a.replace(/^\//,"").split("/");m="/"+c.replace(/^\//,"").split("/").slice(x.length).join("/")}let y=eS(e,{pathname:m}),v=PS(y&&y.map(x=>Object.assign({},x,{params:Object.assign({},l,x.params),pathname:br([a,o.encodeLocation?o.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?a:br([a,o.encodeLocation?o.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),i,n,r);return t&&v?w.createElement(Qs.Provider,{value:{location:ys({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:mn.Pop}},v):v}function SS(){let e=RS(),t=mS(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},t),n?w.createElement("pre",{style:o},n):null,null)}const ES=w.createElement(SS,null);class CS extends w.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?w.createElement(Ks.Provider,{value:this.props.routeContext},w.createElement(Km.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function kS(e){let{routeContext:t,match:n,children:r}=e,o=w.useContext(vS);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),w.createElement(Ks.Provider,{value:t},r)}function PS(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let f=s.findIndex(d=>d.route.id&&(l==null?void 0:l[d.route.id])!==void 0);f>=0||Ue(!1),s=s.slice(0,Math.min(s.length,f+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<s.length;f++){let d=s[f];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=f),d.route.id){let{loaderData:c,errors:m}=n,y=d.route.loader&&c[d.route.id]===void 0&&(!m||m[d.route.id]===void 0);if(d.route.lazy||y){a=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((f,d,c)=>{let m,y=!1,v=null,x=null;n&&(m=l&&d.route.id?l[d.route.id]:void 0,v=d.route.errorElement||ES,a&&(u<0&&c===0?(y=!0,x=null):u===c&&(y=!0,x=d.route.hydrateFallbackElement||null)));let h=t.concat(s.slice(0,c+1)),p=()=>{let g;return m?g=v:y?g=x:d.route.Component?g=w.createElement(d.route.Component,null):d.route.element?g=d.route.element:g=f,w.createElement(kS,{match:d,routeContext:{outlet:f,matches:h,isDataRoute:n!=null},children:g})};return n&&(d.route.ErrorBoundary||d.route.errorElement||c===0)?w.createElement(CS,{location:n.location,revalidation:n.revalidation,component:v,error:m,children:p(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):p()},null)}var $a=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}($a||{});function bS(e){let t=w.useContext(yS);return t||Ue(!1),t}function TS(e){let t=w.useContext(Ks);return t||Ue(!1),t}function NS(e){let t=TS(),n=t.matches[t.matches.length-1];return n.route.id||Ue(!1),n.route.id}function RS(){var e;let t=w.useContext(Km),n=bS($a.UseRouteError),r=NS($a.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Ua(e){Ue(!1)}function _S(e){let{basename:t="/",children:n=null,location:r,navigationType:o=mn.Pop,navigator:i,static:s=!1,future:l}=e;Ju()&&Ue(!1);let a=t.replace(/^\/*/,"/"),u=w.useMemo(()=>({basename:a,navigator:i,static:s,future:ys({v7_relativeSplatPath:!1},l)}),[a,l,i,s]);typeof r=="string"&&(r=Ws(r));let{pathname:f="/",search:d="",hash:c="",state:m=null,key:y="default"}=r,v=w.useMemo(()=>{let x=Hm(f,a);return x==null?null:{location:{pathname:x,search:d,hash:c,state:m,key:y},navigationType:o}},[a,f,d,c,m,y,o]);return v==null?null:w.createElement(Qm.Provider,{value:u},w.createElement(Qs.Provider,{children:n,value:v}))}function OS(e){let{children:t,location:n}=e;return wS(Ba(t),n)}new Promise(()=>{});function Ba(e,t){t===void 0&&(t=[]);let n=[];return w.Children.forEach(e,(r,o)=>{if(!w.isValidElement(r))return;let i=[...t,o];if(r.type===w.Fragment){n.push.apply(n,Ba(r.props.children,i));return}r.type!==Ua&&Ue(!1),!r.props.index||!r.props.children||Ue(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Ba(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const MS="6";try{window.__reactRouterVersion=MS}catch{}const jS="startTransition",Qd=xg[jS];function AS(e){let{basename:t,children:n,future:r,window:o}=e,i=w.useRef();i.current==null&&(i.current=qx({window:o,v5Compat:!0}));let s=i.current,[l,a]=w.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},f=w.useCallback(d=>{u&&Qd?Qd(()=>a(d)):a(d)},[a,u]);return w.useLayoutEffect(()=>s.listen(f),[s,f]),w.createElement(_S,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:s,future:r})}var Kd;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Kd||(Kd={}));var Yd;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Yd||(Yd={}));class LS extends w.Component{constructor(n){super(n);Zs(this,"handleReset",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})});this.state={hasError:!1}}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:S.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 px-4",children:S.jsxs("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center",children:[S.jsx("div",{className:"flex justify-center mb-4",children:S.jsx(Fy,{className:"h-12 w-12 text-red-500"})}),S.jsx("h1",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Something went wrong"}),S.jsx("p",{className:"text-gray-600 mb-6",children:"We're sorry, but something unexpected happened. Please try refreshing the page."}),S.jsxs("button",{onClick:this.handleReset,className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[S.jsx(Ay,{className:"h-4 w-4 mr-2"}),"Try Again"]}),!1]})}):this.props.children}}const IS=({message:e})=>{const t=r=>r.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),n=()=>{console.log("Playing message:",e.content)};return S.jsx("div",{className:`flex ${e.isUser?"justify-end":"justify-start"} animate-fade-in`,children:S.jsxs("div",{className:`max-w-xs lg:max-w-md xl:max-w-lg ${e.isUser?"order-2":""}`,children:[S.jsxs("div",{className:`p-4 rounded-2xl shadow-sm ${e.isUser?"bg-gradient-to-r from-purple-500 to-pink-500 text-white ml-4":"bg-white text-gray-800 mr-4 border border-purple-100"}`,children:[S.jsxs("div",{className:"flex items-start justify-between",children:[S.jsx("p",{className:"text-sm leading-relaxed",children:e.content}),e.isVoice&&S.jsx(_a,{className:`w-4 h-4 ml-2 flex-shrink-0 ${e.isUser?"text-white":"text-purple-500"}`})]}),S.jsxs("div",{className:`flex items-center justify-between mt-2 text-xs ${e.isUser?"text-purple-100":"text-gray-500"}`,children:[S.jsx("span",{children:t(e.timestamp)}),!e.isUser&&S.jsx("button",{onClick:n,className:"hover:text-purple-600 transition-colors p-1 hover:bg-purple-50 rounded",title:"Play message",children:S.jsx(_a,{className:"w-3 h-3"})})]})]}),S.jsx("div",{className:`flex mt-2 ${e.isUser?"justify-end mr-4":"justify-start ml-4"}`,children:S.jsx("div",{className:`w-6 h-6 rounded-full flex items-center justify-center text-xs ${e.isUser?"bg-purple-200 text-purple-700":"bg-gradient-to-r from-pink-400 to-purple-400 text-white"}`,children:e.isUser?S.jsx($y,{className:"w-3 h-3"}):S.jsx(My,{className:"w-3 h-3"})})})]})})},DS=()=>S.jsx("div",{className:"flex justify-start animate-fade-in",children:S.jsxs("div",{className:"max-w-xs lg:max-w-md xl:max-w-lg",children:[S.jsx("div",{className:"bg-white text-gray-800 mr-4 border border-purple-100 p-4 rounded-2xl shadow-sm",children:S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsxs("div",{className:"flex space-x-1",children:[S.jsx("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),S.jsx("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),S.jsx("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),S.jsx("span",{className:"text-xs text-gray-500",children:"AI is typing..."})]})}),S.jsx("div",{className:"flex mt-2 justify-start ml-4",children:S.jsx("div",{className:"w-6 h-6 rounded-full bg-gradient-to-r from-pink-400 to-purple-400 text-white flex items-center justify-center text-xs",children:S.jsx("span",{className:"animate-pulse",children:"💭"})})})]})}),zS=({onRecordingComplete:e,isRecording:t,setIsRecording:n})=>{const[r,o]=w.useState(null),i=w.useRef([]),s=async()=>{try{const a=await navigator.mediaDevices.getUserMedia({audio:!0}),u=new MediaRecorder(a);i.current=[],u.ondataavailable=f=>{f.data.size>0&&i.current.push(f.data)},u.onstop=()=>{const f=new Blob(i.current,{type:"audio/wav"});e(f),a.getTracks().forEach(d=>d.stop())},u.start(),o(u),n(!0)}catch(a){console.error("Error accessing microphone:",a),alert("Could not access microphone. Please check your permissions.")}},l=()=>{r&&r.state==="recording"&&(r.stop(),n(!1),o(null))};return S.jsx("div",{className:"flex items-center space-x-2",children:t?S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsxs("div",{className:"flex items-center space-x-2 bg-red-100 px-3 py-2 rounded-full",children:[S.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),S.jsx("span",{className:"text-red-600 text-sm font-medium",children:"Recording..."})]}),S.jsx("button",{onClick:l,className:"p-3 bg-red-500 text-white rounded-full hover:bg-red-600 transition-all duration-200 shadow-lg hover:shadow-xl",title:"Stop recording",children:S.jsx(Dy,{className:"w-5 h-5"})})]}):S.jsx("button",{onClick:s,className:"p-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl",title:"Hold to record voice message",children:S.jsx(jy,{className:"w-5 h-5"})})})},FS=({config:e,onConfigUpdate:t,onTest:n,isOpen:r,onClose:o})=>{const[i,s]=w.useState(e),[l,a]=w.useState(!1),u=()=>{t(i),o()},f=async()=>{if(n){a(!0);try{await n()}finally{a(!1)}}};return r?S.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:S.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[S.jsxs("div",{className:"flex items-center justify-between p-6 border-b",children:[S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx(Gh,{className:"w-5 h-5 text-purple-600"}),S.jsx("h2",{className:"text-lg font-semibold",children:"Langflow Settings"})]}),S.jsx("button",{onClick:o,className:"text-gray-400 hover:text-gray-600",children:"×"})]}),S.jsxs("div",{className:"p-6 space-y-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Langflow Base URL"}),S.jsx("input",{type:"text",value:i.baseUrl,onChange:d=>s(c=>({...c,baseUrl:d.target.value})),placeholder:"http://127.0.0.1:7860",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Flow ID"}),S.jsx("input",{type:"text",value:i.flowId,onChange:d=>s(c=>({...c,flowId:d.target.value})),placeholder:"8b59d93f-02bc-40a0-9adc-a5c7ea18b860",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key (Optional)"}),S.jsx("input",{type:"password",value:i.apiKey||"",onChange:d=>s(c=>({...c,apiKey:d.target.value})),placeholder:"sk-...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"}),S.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Only required if authentication is enabled in Langflow"})]})]}),S.jsxs("div",{className:"flex items-center justify-between p-6 border-t bg-gray-50",children:[S.jsxs("button",{onClick:f,disabled:l,className:"flex items-center space-x-2 px-4 py-2 text-purple-600 border border-purple-600 rounded-md hover:bg-purple-50 disabled:opacity-50",children:[S.jsx(zy,{className:"w-4 h-4"}),S.jsx("span",{children:l?"Testing...":"Test Connection"})]}),S.jsxs("div",{className:"flex space-x-2",children:[S.jsx("button",{onClick:o,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),S.jsxs("button",{onClick:u,className:"flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700",children:[S.jsx(Ly,{className:"w-4 h-4"}),S.jsx("span",{children:"Save"})]})]})]})]})}):null};class $S{constructor(t){Zs(this,"config");this.config=t}async sendMessage(t,n,r){if(!(t!=null&&t.trim()))throw new Error("Message cannot be empty");if(!this.config.baseUrl||!this.config.flowId)throw new Error("Langflow configuration is incomplete. Please check baseUrl and flowId.");const o={input_value:t.trim(),output_type:"chat",input_type:"chat",session_id:n||this.generateSessionId(),...r},i={method:"POST",headers:{"Content-Type":"application/json",...this.config.apiKey&&{"x-api-key":this.config.apiKey}},body:JSON.stringify(o),signal:AbortSignal.timeout(3e4)};try{const s=await fetch(`${this.config.baseUrl}/api/v1/run/${this.config.flowId}`,i);if(!s.ok){const a=await s.text().catch(()=>"Unknown error");throw new Error(`HTTP ${s.status}: ${a}`)}const l=await s.json();if(!l||typeof l!="object")throw new Error("Invalid response format from Langflow");return l}catch(s){if(s instanceof Error){if(s.name==="AbortError")throw new Error("Request timeout - please try again");if(s.message.includes("fetch"))throw new Error("Network error - please check your connection and Langflow server")}throw console.error("Error sending message to Langflow:",s),s}}async sendMessageStream(t,n,r,o,i){var f,d,c;if(!(t!=null&&t.trim()))throw new Error("Message cannot be empty");if(!this.config.baseUrl||!this.config.flowId)throw new Error("Langflow configuration is incomplete. Please check baseUrl and flowId.");const s={input_value:t.trim(),output_type:"chat",input_type:"chat",session_id:n||this.generateSessionId(),...i},l=new AbortController,a=setTimeout(()=>l.abort(),6e4),u={method:"POST",headers:{"Content-Type":"application/json",accept:"application/json",...this.config.apiKey&&{"x-api-key":this.config.apiKey}},body:JSON.stringify(s),signal:l.signal};try{const m=await fetch(`${this.config.baseUrl}/api/v1/run/${this.config.flowId}?stream=true`,u);if(!m.ok){const h=await m.text().catch(()=>"Unknown error");throw new Error(`HTTP ${m.status}: ${h}`)}const y=(f=m.body)==null?void 0:f.getReader();if(!y)throw new Error("Streaming not supported - no response body reader available");const v=new TextDecoder;let x="";try{for(;;){const{done:h,value:p}=await y.read();if(h)break;x+=v.decode(p,{stream:!0});const g=x.split(`
`);x=g.pop()||"";for(const E of g)if(E.trim())try{const C=JSON.parse(E);C.event==="token"&&r&&((d=C.data)!=null&&d.chunk)?r(C.data.chunk):C.event==="end"&&o&&((c=C.data)!=null&&c.result)&&o(C.data.result)}catch(C){console.warn("Failed to parse streaming event:",E,C)}}}finally{y.releaseLock(),clearTimeout(a)}}catch(m){if(clearTimeout(a),m instanceof Error){if(m.name==="AbortError")throw new Error("Streaming request timeout - please try again");if(m.message.includes("fetch"))throw new Error("Network error during streaming - please check your connection")}throw console.error("Error streaming message from Langflow:",m),m}}extractMessageText(t){var n,r,o,i,s,l;try{if(!t||typeof t!="object")return console.warn("Invalid response object provided to extractMessageText"),"";const a=(l=(s=(i=(o=(r=(n=t.outputs)==null?void 0:n[0])==null?void 0:r.outputs)==null?void 0:o[0])==null?void 0:i.results)==null?void 0:s.message)==null?void 0:l.text;return typeof a=="string"?a.trim():(console.warn("No valid text found in Langflow response"),"")}catch(a){return console.error("Error extracting message text:",a),""}}generateSessionId(){return`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}updateConfig(t){this.config={...this.config,...t}}}const US=(e={})=>{const[t,n]=w.useState({isLoading:!1,error:null,sessionId:e.sessionId||`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}),r=w.useRef(new $S({baseUrl:"http://127.0.0.1:7860",flowId:"8b59d93f-02bc-40a0-9adc-a5c7ea18b860",apiKey:void 0,...e.config})).current,o=w.useCallback(async(a,u,f)=>{n(d=>({...d,isLoading:!0,error:null}));try{if(e.enableStreaming&&f){let d="";return await r.current.sendMessageStream(a,t.sessionId,c=>{d+=c,f(c)},c=>{const m=r.current.extractMessageText(c);m&&m!==d&&(d=m),u&&u(d)}),n(c=>({...c,isLoading:!1})),d}else{const d=await r.current.sendMessage(a,t.sessionId),c=r.current.extractMessageText(d);return u&&u(c),n(m=>({...m,isLoading:!1})),c}}catch(d){const c=d instanceof Error?d.message:"Unknown error occurred";throw n(m=>({...m,isLoading:!1,error:c})),d}},[t.sessionId,e.enableStreaming,r]),i=w.useCallback(()=>{n(a=>({...a,error:null}))},[]),s=w.useCallback(a=>{r.current.updateConfig(a)},[r]),l=w.useCallback(()=>{const a=`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;n(u=>({...u,sessionId:a}))},[]);return{sendMessage:o,clearError:i,updateConfig:s,resetSession:l,isLoading:t.isLoading,error:t.error,sessionId:t.sessionId}},BS=()=>{const[e,t]=w.useState([{id:"1",content:"Hey there! I'm your AI companion powered by Langflow. How can I help you today? 💕",isUser:!1,timestamp:new Date}]),[n,r]=w.useState(""),[o,i]=w.useState(!1),[s,l]=w.useState(""),[a,u]=w.useState(!1),f=w.useRef(null),d=w.useRef(null),{sendMessage:c,isLoading:m,error:y,clearError:v,sessionId:x,updateConfig:h}=US({enableStreaming:!0}),p=()=>{var P;(P=f.current)==null||P.scrollIntoView({behavior:"smooth"})};w.useEffect(()=>{p()},[e]);const g=()=>Math.random().toString(36).substr(2,9),E=async P=>{const j=g(),M={id:j,content:"",isUser:!1,timestamp:new Date};t(F=>[...F,M]),l("");try{await c(P,F=>{t(L=>L.map($=>$.id===j?{...$,content:F}:$)),l("")},F=>{l(L=>L+F),t(L=>L.map($=>$.id===j?{...$,content:s+F}:$))})}catch(F){console.error("Error getting Langflow response:",F),t(L=>L.map($=>$.id===j?{...$,content:"Sorry, I encountered an error. Please try again."}:$))}},C=async()=>{if(!n.trim()||m)return;const P={id:g(),content:n,isUser:!0,timestamp:new Date};t(M=>[...M,P]);const j=n;r(""),y&&v(),await E(j)},b=P=>{P.key==="Enter"&&!P.shiftKey&&(P.preventDefault(),C())},T=async P=>{const j={id:g(),content:"Voice message received! (This would be transcribed text)",isUser:!0,timestamp:new Date,isVoice:!0};t(M=>[...M,j]),await E("Voice message received")};return S.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-100 via-pink-50 to-violet-100",children:[S.jsx("div",{className:"bg-white/80 backdrop-blur-sm border-b border-purple-200 px-6 py-4 shadow-sm",children:S.jsxs("div",{className:"max-w-4xl mx-auto flex items-center justify-between",children:[S.jsxs("div",{className:"flex items-center space-x-3",children:[S.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center",children:S.jsx("span",{className:"text-white font-semibold",children:"AI"})}),S.jsxs("div",{children:[S.jsx("h1",{className:"text-xl font-semibold text-gray-800",children:"Langflow AI Companion"}),S.jsxs("p",{className:"text-sm flex items-center",children:[S.jsx("span",{className:`w-2 h-2 rounded-full mr-2 ${y?"bg-red-500":"bg-green-500 animate-pulse"}`}),y?"Connection Error":"Connected to Langflow"]})]})]}),S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx("button",{onClick:()=>u(!0),className:"p-2 hover:bg-purple-100 rounded-full transition-colors",children:S.jsx(Gh,{className:"w-5 h-5 text-purple-600"})}),S.jsx("button",{className:"p-2 hover:bg-purple-100 rounded-full transition-colors",children:S.jsx(_a,{className:"w-5 h-5 text-purple-600"})})]})]})}),y&&S.jsx("div",{className:"bg-red-50 border-b border-red-200 px-6 py-3",children:S.jsxs("div",{className:"max-w-4xl mx-auto flex items-center justify-between",children:[S.jsxs("div",{className:"flex items-center space-x-2 text-red-700",children:[S.jsx(Oy,{className:"w-4 h-4"}),S.jsxs("span",{className:"text-sm",children:["Connection error: ",y]})]}),S.jsx("button",{onClick:v,className:"text-red-600 hover:text-red-800 text-sm font-medium",children:"Dismiss"})]})}),S.jsx("div",{className:"max-w-4xl mx-auto px-6 py-6 min-h-[calc(100vh-180px)] max-h-[calc(100vh-180px)] overflow-y-auto",children:S.jsxs("div",{className:"space-y-4",children:[e.map(P=>S.jsx(IS,{message:P},P.id)),m&&S.jsx(DS,{}),S.jsx("div",{ref:f})]})}),S.jsx("div",{className:"bg-white/80 backdrop-blur-sm border-t border-purple-200 px-6 py-4 sticky bottom-0",children:S.jsx("div",{className:"max-w-4xl mx-auto",children:S.jsxs("div",{className:"flex items-end space-x-4",children:[S.jsxs("div",{className:"flex-1 relative",children:[S.jsx("input",{ref:d,type:"text",value:n,onChange:P=>r(P.target.value),onKeyPress:b,placeholder:"Type your message...",className:"w-full px-4 py-3 pr-12 border border-purple-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent bg-white/90 text-gray-800 placeholder-gray-500",disabled:o}),S.jsx("button",{onClick:C,disabled:!n.trim()||o||m,className:"absolute right-2 top-1/2 transform -translate-y-1/2 p-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:S.jsx(Iy,{className:"w-4 h-4"})})]}),S.jsx(zS,{onRecordingComplete:T,isRecording:o,setIsRecording:i})]})})}),S.jsx(FS,{config:{baseUrl:"http://127.0.0.1:7860",flowId:"8b59d93f-02bc-40a0-9adc-a5c7ea18b860",apiKey:void 0},onConfigUpdate:h,onTest:async()=>{try{await c("Hello, this is a test message.")}catch{throw new Error("Connection test failed")}},isOpen:a,onClose:()=>u(!1)})]})},VS=()=>{const e=Ym();return w.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),S.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:S.jsxs("div",{className:"text-center",children:[S.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),S.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),S.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},HS=new Yx({defaultOptions:{queries:{retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:5*60*1e3},mutations:{retry:1}}}),WS=()=>S.jsx(LS,{children:S.jsx(Xx,{client:HS,children:S.jsxs(Px,{children:[S.jsx(Cw,{}),S.jsx(Zw,{}),S.jsx(AS,{children:S.jsxs(OS,{children:[S.jsx(Ua,{path:"/",element:S.jsx(BS,{})}),S.jsx(Ua,{path:"*",element:S.jsx(VS,{})})]})})]})})});yh(document.getElementById("root")).render(S.jsx(WS,{}));
