import { useState, useCallback, useRef } from 'react';
import { LangflowService, LangflowConfig, LangflowResponse } from '../lib/langflow';

export interface UseLangflowOptions {
  config?: Partial<LangflowConfig>;
  sessionId?: string;
  enableStreaming?: boolean;
}

export interface LangflowState {
  isLoading: boolean;
  error: string | null;
  sessionId: string;
}

export const useLangflow = (options: UseLangflowOptions = {}) => {
  const [state, setState] = useState<LangflowState>({
    isLoading: false,
    error: null,
    sessionId: options.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  });

  // Create Langflow service instance
  const langflowService = useRef(
    new LangflowService({
      baseUrl: import.meta.env.VITE_LANGFLOW_BASE_URL || 'http://127.0.0.1:7860',
      flowId: import.meta.env.VITE_LANGFLOW_FLOW_ID || '8b59d93f-02bc-40a0-9adc-a5c7ea18b860',
      apiKey: import.meta.env.VITE_LANGFLOW_API_KEY,
      ...options.config,
    })
  ).current;

  const sendMessage = useCallback(
    async (
      message: string,
      onResponse?: (response: string) => void,
      onToken?: (token: string) => void
    ): Promise<string> => {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      try {
        if (options.enableStreaming && onToken) {
          // Use streaming for real-time token updates
          let fullResponse = '';

          await langflowService.current.sendMessageStream(
            message,
            state.sessionId,
            (token: string) => {
              fullResponse += token;
              onToken(token);
            },
            (response: LangflowResponse) => {
              const responseText = langflowService.current.extractMessageText(response);
              if (responseText && responseText !== fullResponse) {
                fullResponse = responseText;
              }
              if (onResponse) {
                onResponse(fullResponse);
              }
            }
          );

          setState(prev => ({ ...prev, isLoading: false }));
          return fullResponse;
        } else {
          // Use regular API call
          const response = await langflowService.current.sendMessage(message, state.sessionId);
          const responseText = langflowService.current.extractMessageText(response);

          if (onResponse) {
            onResponse(responseText);
          }

          setState(prev => ({ ...prev, isLoading: false }));
          return responseText;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: errorMessage
        }));
        throw error;
      }
    },
    [state.sessionId, options.enableStreaming, langflowService]
  );

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const updateConfig = useCallback((newConfig: Partial<LangflowConfig>) => {
    langflowService.current.updateConfig(newConfig);
  }, [langflowService]);

  const resetSession = useCallback(() => {
    const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setState(prev => ({ ...prev, sessionId: newSessionId }));
  }, []);

  return {
    sendMessage,
    clearError,
    updateConfig,
    resetSession,
    isLoading: state.isLoading,
    error: state.error,
    sessionId: state.sessionId,
  };
};
