export interface Assumptions {
    /**
     * https://babeljs.io/docs/en/assumptions#arraylikeisiterable
     */
    arrayLikeIsIterable?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#constantreexports
     */
    constantReexports?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#constantsuper
     */
    constantSuper?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#enumerablemodulemeta
     */
    enumerableModuleMeta?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#ignorefunctionlength
     */
    ignoreFunctionLength?: boolean;
    ignoreFunctionName?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#ignoretoprimitivehint
     */
    ignoreToPrimitiveHint?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#iterableisarray
     */
    iterableIsArray?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#mutabletemplateobject
     */
    mutableTemplateObject?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#noclasscalls
     */
    noClassCalls?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#nodocumentall
     */
    noDocumentAll?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#noincompletensimportdetection
     */
    noIncompleteNsImportDetection?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#nonewarrows
     */
    noNewArrows?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#objectrestnosymbols
     */
    objectRestNoSymbols?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#privatefieldsasproperties
     */
    privateFieldsAsProperties?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#puregetters
     */
    pureGetters?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#setclassmethods
     */
    setClassMethods?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#setcomputedproperties
     */
    setComputedProperties?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#setpublicclassfields
     */
    setPublicClassFields?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#setspreadproperties
     */
    setSpreadProperties?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#skipforofiteratorclosing
     */
    skipForOfIteratorClosing?: boolean;
    /**
     * https://babeljs.io/docs/en/assumptions#superiscallableconstructor
     */
    superIsCallableConstructor?: boolean;
    /**
     * @deprecated This value will be always true
     */
    tsEnumIsReadonly?: boolean;
}
//# sourceMappingURL=assumptions.d.ts.map